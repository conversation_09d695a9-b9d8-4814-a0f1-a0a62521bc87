// Test using the same method as the frontend (supabase.functions.invoke)
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://ygkxdctaraeragizxfbt.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testFrontendMethod() {
  console.log('🧪 Testing with frontend method (supabase.functions.invoke)...');
  
  try {
    console.log('🚀 Sending chat request:', {
      question: "What are the side effects of aspirin?",
      medicineName: "Aspirin",
      userId: "fc37eb65-c52a-4e9e-a718-f2d2b4d8d324"
    });

    const response = await supabase.functions.invoke('medical-chat', {
      body: {
        question: "What are the side effects of aspirin?",
        medicineName: "Aspirin",
        userId: "fc37eb65-c52a-4e9e-a718-f2d2b4d8d324"
      }
    });

    console.log('📥 Chat response received:', response);

    if (response.error) {
      console.error('❌ Function invocation error:', response.error);
      console.error('❌ Error details:', {
        message: response.error.message,
        stack: response.error.stack,
        name: response.error.name
      });
      return;
    }

    if (!response.data) {
      console.error('❌ No data in response:', response);
      return;
    }

    if (!response.data.success) {
      console.error('❌ Chat function returned error:', response.data);
      return;
    }

    console.log('✅ CHATBOT IS WORKING PERFECTLY!');
    console.log('💬 AI Response:', response.data.reply);
    console.log('⏱️ Response time:', response.data.responseTime + 'ms');
    console.log('🆔 Session ID:', response.data.sessionId);
    
  } catch (error) {
    console.log('❌ Network error:', error.message);
    console.error('❌ Full error:', error);
  }
}

testFrontendMethod();
