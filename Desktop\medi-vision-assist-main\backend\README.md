
# MediVision Backend API

AI-powered medical chatbot backend with OCR capabilities for medicine identification and guidance.

## 🚀 IMPORTANT: Server Setup Instructions

**The frontend application requires this backend server to be running for OCR functionality to work properly.**

### Quick Setup (Essential Steps)

1. **Navigate to the backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the server:**
   ```bash
   # For development (recommended)
   npm run dev

   # OR for production
   npm start
   ```

4. **Verify the server is running:**
   - Open your browser and visit: http://localhost:3001/api/health
   - You should see a JSON response with `"status": "healthy"`
   - If you see this, the OCR service is ready!

### ⚠️ Troubleshooting

If you see `net::ERR_CONNECTION_REFUSED` errors in the frontend:
1. Make sure you've run `npm install` in the backend directory
2. Make sure the server is running with `npm run dev`
3. Check that port 3001 is not blocked by firewall
4. Verify the health endpoint responds at http://localhost:3001/api/health

## Features

- **🔍 OCR Processing**: Server-side Tesseract.js with Sharp image preprocessing
  - Multi-language support (English, French, German, Arabic)
  - Advanced image preprocessing pipeline
  - Intelligent caching system
  - Medicine name extraction and recognition
- **💬 Intelligent Chat System**: Context-aware medical conversations
- **🎯 Intent Recognition**: Automatic classification of user queries
- **📝 Response Generation**: Smart, helpful medical guidance
- **💾 Conversation History**: Session-based chat memory
- **🛡️ Error Handling**: Robust error management and logging
- **⚡ Performance**: Optimized for high-throughput OCR processing

## Dependencies

The server now includes additional dependencies for OCR functionality:

- **multer**: File upload handling for images
- **tesseract.js**: OCR text extraction engine
- **sharp**: High-performance image processing and preprocessing

These are automatically installed when you run `npm install`.

## API Endpoints

### Health Check
- `GET /health` - Basic health check
- `GET /api/health` - OCR service health check (returns `"status": "healthy"`)

### Chat
- `POST /api/chat/message` - Send a message to the chatbot
- `GET /api/chat/conversation/:sessionId` - Get conversation history
- `DELETE /api/chat/conversation/:sessionId` - Clear conversation

### OCR (New!)
- `POST /api/ocr/process` - Process image with server-side OCR
- `POST /api/ocr/cache/clear` - Clear OCR cache
- `GET /api/ocr/status` - Get OCR service status

### Example OCR Request
```bash
curl -X POST http://localhost:3001/api/ocr/process \
  -F "image=@medicine_photo.jpg"
```

### Example OCR Response
```json
{
  "success": true,
  "text": "FERVEX ADULTES SANS SUCRE",
  "confidence": 87.5,
  "processingTime": 3240,
  "medicineNames": ["FERVEX"],
  "words": [...],
  "cached": false
}
```

### Example Chat Request
```json
POST /api/chat/message
{
  "message": "Hi, what is this app?",
  "sessionId": "user123"
}
```

### Example Chat Response
```json
{
  "success": true,
  "data": {
    "response": "Our app helps you identify medicines...",
    "intent": "app_info",
    "confidence": 0.95,
    "suggestions": ["Upload a photo", "Search by name"],
    "sessionId": "user123"
  }
}
```

## Supported Intents

- **greet**: Welcome messages
- **ask_bot_status**: Bot status queries
- **app_info**: App purpose and features
- **symptom_query**: Health symptom discussions
- **medicine_identification**: Medicine identification requests
- **dosage_question**: Dosage and usage queries
- **side_effects**: Side effect information
- **emergency**: Emergency situations

## Project Structure

```
backend/
├── server.js              # Main application entry
├── routes/
│   └── chatRoutes.js      # Chat API routes
├── controllers/
│   └── chatController.js  # Request handling logic
├── services/
│   ├── intentService.js   # Intent detection
│   ├── responseService.js # Response generation
│   └── conversationService.js # Chat history
├── config/
│   └── intents.js         # Intent definitions
├── middleware/
│   ├── validation.js      # Input validation
│   └── errorHandler.js    # Error handling
└── package.json
```

## Future AI Integration

The backend is structured to easily integrate with:
- OpenAI GPT models
- Google Dialogflow
- Custom ML models
- External medical APIs

Uncomment and configure the relevant environment variables in `.env` when ready.

## Development

Run with auto-reload:
```bash
npm run dev
```

The server will restart automatically when you make changes to the code.
