-- URGENT: Fix infinite recursion in RLS policies
-- Run this in Supabase Dashboard > SQL Editor

-- Step 1: Drop the problematic admin policies that cause infinite recursion
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all scans" ON public.medicine_scans;
DROP POLICY IF EXISTS "Ad<PERSON> can view all chat sessions" ON public.chat_sessions;
DROP POLICY IF EXISTS "Ad<PERSON> can view all feedback" ON public.chat_feedback;
DROP POLICY IF EXISTS "Ad<PERSON> can update feedback" ON public.chat_feedback;
DROP POLICY IF EXISTS "Ad<PERSON> can view all logs" ON public.system_logs;

-- Step 2: Create a function to check if user is admin (avoids recursion)
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  -- Use a direct query with SECURITY DEFINER to bypass RLS
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.is_admin TO authenticated;

-- Step 4: Recreate admin policies using the function (no recursion)
CREATE POLICY "Admins can view all profiles" ON public.profiles
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can view all scans" ON public.medicine_scans
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can view all chat sessions" ON public.chat_sessions
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can view all feedback" ON public.chat_feedback
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can update feedback" ON public.chat_feedback
  FOR UPDATE USING (public.is_admin());

CREATE POLICY "Admins can view all logs" ON public.system_logs
  FOR SELECT USING (public.is_admin());

-- Step 5: Add a policy to allow service role to insert into profiles (for the trigger)
CREATE POLICY "Service role can insert profiles" ON public.profiles
  FOR INSERT WITH CHECK (true);

-- Step 6: Ensure the trigger function has proper permissions
ALTER FUNCTION public.handle_new_user() SECURITY DEFINER;

-- Step 7: Add some helpful indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_admin ON public.profiles(is_admin) WHERE is_admin = true;
CREATE INDEX IF NOT EXISTS idx_profiles_id ON public.profiles(id);

-- Step 8: Add a comment explaining the fix
COMMENT ON FUNCTION public.is_admin IS 'Checks if a user is admin without causing RLS recursion. Uses SECURITY DEFINER to bypass RLS.';

-- Verification query - this should not cause infinite recursion
SELECT 'RLS fix applied successfully' as status;
