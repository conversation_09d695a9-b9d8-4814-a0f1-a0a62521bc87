const { createWorker } = require('tesseract.js');
const sharp = require('sharp');

class OCRController {
  constructor() {
    this.worker = null;
    this.isInitialized = false;
    this.cache = new Map();
    this.initializeWorker();
  }

  async initializeWorker() {
    try {
      console.log('🔄 Initializing server-side Tesseract worker...');
      
      // Initialize with multiple languages
      this.worker = await createWorker(['eng', 'fra', 'deu', 'ara'], 1, {
        logger: m => {
          if (m.status === 'recognizing text') {
            console.log(`OCR Progress: ${Math.round(m.progress * 100)}%`);
          }
        }
      });

      // Set optimized parameters during initialization
      await this.worker.setParameters({
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 .-+%®™&/()[]',
        tessedit_pageseg_mode: '6', // Uniform block of text
        tessedit_ocr_engine_mode: '1', // LSTM only
        preserve_interword_spaces: '1',
        user_defined_dpi: '300'
      });

      this.isInitialized = true;
      console.log('✅ Server-side Tesseract worker initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Tesseract worker:', error);
      this.isInitialized = false;
    }
  }

  async processImage(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'No image file provided'
        });
      }

      if (!this.isInitialized || !this.worker) {
        await this.initializeWorker();
        if (!this.isInitialized) {
          return res.status(500).json({
            success: false,
            error: 'OCR service not available'
          });
        }
      }

      const startTime = Date.now();
      console.log(`🚀 Processing image: ${req.file.originalname} (${req.file.size} bytes)`);

      // Generate cache key
      const cacheKey = this.generateCacheKey(req.file.buffer);
      
      // Check cache first
      if (this.cache.has(cacheKey)) {
        console.log('📦 Returning cached result');
        const cachedResult = this.cache.get(cacheKey);
        return res.json({
          ...cachedResult,
          cached: true
        });
      }

      // Preprocess image with Sharp
      const processedImage = await this.preprocessImage(req.file.buffer);

      // Perform OCR
      const { data } = await this.worker.recognize(processedImage);
      const processingTime = Date.now() - startTime;

      // Extract potential medicine names
      const medicineNames = this.extractMedicineNames(data.text);

      const result = {
        success: true,
        text: data.text.trim(),
        confidence: data.confidence,
        processingTime,
        medicineNames,
        words: data.words || [],
        cached: false
      };

      // Cache the result
      this.cache.set(cacheKey, result);
      
      // Limit cache size
      if (this.cache.size > 100) {
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }

      console.log(`✅ OCR completed in ${processingTime}ms with ${data.confidence.toFixed(1)}% confidence`);
      console.log(`💊 Medicine names found: ${medicineNames.join(', ')}`);

      res.json(result);

    } catch (error) {
      console.error('❌ OCR processing failed:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'OCR processing failed',
        text: '',
        confidence: 0,
        processingTime: 0,
        medicineNames: [],
        words: [],
        cached: false
      });
    }
  }

  async preprocessImage(imageBuffer) {
    try {
      // Use Sharp for high-quality image preprocessing
      const processedBuffer = await sharp(imageBuffer)
        .resize(null, 800, { 
          withoutEnlargement: true,
          kernel: sharp.kernel.lanczos3 
        })
        .sharpen({ sigma: 1, m1: 0.5, m2: 2 })
        .normalize()
        .modulate({ 
          brightness: 1.1,
          saturation: 0.8,
          hue: 0 
        })
        .png({ quality: 95 })
        .toBuffer();

      console.log('✅ Image preprocessing completed with Sharp');
      return processedBuffer;
    } catch (error) {
      console.warn('⚠️ Image preprocessing failed, using original:', error);
      return imageBuffer;
    }
  }

  extractMedicineNames(text) {
    const cleanText = text.replace(/[^\w\s-]/g, ' ').trim();
    const words = cleanText.split(/\s+/).filter(word => word.length > 2);
    
    // Simple heuristic to identify potential medicine names
    const potentialNames = words.filter(word => {
      // Look for capitalized words or common medicine patterns
      return /^[A-Z][a-z]+/.test(word) || 
             /^[A-Z]{2,}$/.test(word) ||
             word.toLowerCase().includes('mg') ||
             word.toLowerCase().includes('ml');
    });

    return [...new Set(potentialNames)]; // Remove duplicates
  }

  generateCacheKey(buffer) {
    // Simple hash function for cache key
    let hash = 0;
    const str = buffer.toString('base64').substring(0, 100);
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  async clearCache(req, res) {
    try {
      this.cache.clear();
      console.log('✅ OCR cache cleared');
      res.json({
        success: true,
        message: 'Cache cleared successfully'
      });
    } catch (error) {
      console.error('❌ Failed to clear cache:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to clear cache'
      });
    }
  }

  async getStatus(req, res) {
    try {
      res.json({
        status: 'healthy',
        initialized: this.isInitialized,
        cacheSize: this.cache.size,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        status: 'error',
        error: error.message
      });
    }
  }
}

// Create singleton instance
const ocrController = new OCRController();

module.exports = {
  processImage: (req, res) => ocrController.processImage(req, res),
  clearCache: (req, res) => ocrController.clearCache(req, res),
  getStatus: (req, res) => ocrController.getStatus(req, res)
};
