/**
 * Unified Medical Knowledge Base for MediVision Chatbot
 * This is the SINGLE SOURCE OF TRUTH for all Q&A responses
 * Used by both client-side fallback and server-side processing
 */

export interface MedicalKnowledge {
  question: string;
  response: string;
  keywords: string[];
  category: string;
}

export const medicalKnowledgeBase: MedicalKnowledge[] = [
  // Identity and Bot Information - Enhanced Dataset
  {
    question: "Who created you?",
    response: "I was developed by Med <PERSON>.",
    keywords: ["created", "made", "developed", "built", "creator", "developer", "who", "create"],
    category: "identity"
  },
  {
    question: "Who made you?",
    response: "I was developed by Med <PERSON>.",
    keywords: ["made", "created", "developed", "built", "maker", "creator", "who", "make"],
    category: "identity"
  },
  {
    question: "Who are you?",
    response: "I'm MediVision, your medical assistant chatbot developed by Med <PERSON>. I help with medical questions and medicine identification.",
    keywords: ["who", "are", "you", "identity", "name"],
    category: "identity"
  },
  {
    question: "What is your name?",
    response: "My name is MediV<PERSON>. I'm your medical assistant chatbot developed by Me<PERSON>.",
    keywords: ["name", "called", "identity", "what", "your"],
    category: "identity"
  },
  {
    question: "How can you help me?",
    response: "I'm MediVision, your medical assistant developed by Med Amine Chouchane. I can help you with: 🔹 Medicine information and identification 🔹 Symptoms and health conditions guidance 🔹 <PERSON> wellness advice 🔹 Drug interactions and side effects 🔹 First aid information. What specific medical question do you have?",
    keywords: ["how", "can", "you", "help", "me", "assist", "capability"],
    category: "identity"
  },
  {
    question: "Did Med Amine create you?",
    response: "Yes, Med Amine Chouchane created me. I'm MediVision, your medical assistant.",
    keywords: ["did", "med", "amine", "create", "you", "yes", "confirm"],
    category: "identity"
  },

  // Greetings and Small Talk
  {
    question: "Hello",
    response: "Hello! I'm MediVision, your medical assistant chatbot. How can I help you with your health questions today?",
    keywords: ["hello", "hi", "hey", "greetings"],
    category: "greeting"
  },
  {
    question: "How are you?",
    response: "I'm functioning well, thank you for asking! How can I assist you with your health questions today?",
    keywords: ["how", "are", "you", "greeting", "well"],
    category: "greeting"
  },
  {
    question: "Good morning",
    response: "Good morning! I'm MediVision, ready to assist you with medical information. How can I help you today?",
    keywords: ["good", "morning", "greeting"],
    category: "greeting"
  },
  {
    question: "Thank you",
    response: "You're very welcome! I'm happy to help with any medical questions you might have. Feel free to ask me anything about health or medicines.",
    keywords: ["thank", "you", "thanks", "appreciation"],
    category: "greeting"
  },

  // Medical Questions - Core Dataset
  {
    question: "What is aspirin used for?",
    response: "I'm MediVision, your medical assistant. Aspirin is commonly used for pain relief, fever reduction, and inflammation. It's also used in low doses for heart attack and stroke prevention. Always consult your doctor for proper dosage.",
    keywords: ["aspirin", "pain", "fever", "inflammation", "heart"],
    category: "pain_relief"
  },
  {
    question: "How do I manage diabetes?",
    response: "Diabetes management includes monitoring blood sugar, taking prescribed medications, eating a balanced diet, regular exercise, and routine medical check-ups. Work closely with your healthcare team for the best results.",
    keywords: ["diabetes", "management", "blood sugar", "diet", "exercise"],
    category: "diabetes"
  },
  {
    question: "What are signs of depression?",
    response: "Depression signs include persistent sadness, loss of interest in activities, fatigue, sleep changes, and difficulty concentrating. If you're experiencing these symptoms, please reach out to a mental health professional.",
    keywords: ["depression", "sadness", "fatigue", "sleep", "mental health"],
    category: "mental_health"
  },
  {
    question: "What helps with headaches?",
    response: "Headache relief may include rest in a dark room, hydration, over-the-counter pain relievers, and stress management. If headaches are frequent or severe, consult a healthcare provider for proper evaluation.",
    keywords: ["headache", "rest", "dark room", "hydration", "stress"],
    category: "common_conditions"
  }
];

// Greeting responses
export const greetingResponses = [
  "Hello! I'm MediVision, your medical assistant chatbot. How can I help you with your health questions today?",
  "Hi there! I'm MediVision, ready to assist you with medical information and health guidance. What can I help you with?",
  "Yes, I'm MediVision — your AI assistant for health and medical guidance. What would you like to know?"
];

// Error responses
export const errorResponses = {
  unclear: "I'm not sure I understood that. Please ask your question in English. Arabic and German support is coming soon!",
  nonMedical: "I'm here to help with medical and health-related questions only. For other topics, I recommend using a general-purpose assistant.",
  language: "Please write your question in English. We are working to support Arabic and German very soon!",
  cors: "I'm MediVision, your medical assistant. I can help with questions about medicines, health conditions, symptoms, and general wellness. Please ask me a specific medical question, and I'll do my best to help."
};

/**
 * UNIFIED RESPONSE FUNCTION - Single source of truth for all responses
 * This function is used by both client-side and server-side systems
 */
export function findBestMatch(question: string): string {
  const lowerQuestion = question.toLowerCase().trim();

  console.log(`🔍 FindBestMatch called with: "${question}"`);

  // PRIORITY 1: Check for identity questions first (highest priority)
  const identityKeywords = [
    'who created', 'who made', 'who developed', 'who built', 'who designed',
    'your creator', 'your developer', 'your maker', 'created you', 'made you',
    'developed you', 'built you', 'designed you', 'who is your creator',
    'who is your developer', 'creator name', 'developer name', 'who create'
  ];

  for (const keyword of identityKeywords) {
    if (lowerQuestion.includes(keyword)) {
      console.log(`🎯 Identity keyword detected: "${keyword}"`);
      return "I was developed by Med Amine Chouchane.";
    }
  }

  // PRIORITY 2: Check for greetings
  const greetingKeywords = ['hi', 'hello', 'hey', 'greetings', 'can you talk', 'are you there'];
  if (greetingKeywords.some(keyword => lowerQuestion.includes(keyword))) {
    console.log(`👋 Greeting detected`);
    return greetingResponses[Math.floor(Math.random() * greetingResponses.length)];
  }

  // PRIORITY 3: Check for "who are you" type questions
  if (lowerQuestion.includes('who are you') || lowerQuestion.includes('what are you')) {
    console.log(`🤖 "Who are you" question detected`);
    return "I'm MediVision, your medical assistant chatbot developed by Med Amine Chouchane. I help with medical questions and medicine identification.";
  }

  // PRIORITY 4: Check for name questions
  if (lowerQuestion.includes('your name') || lowerQuestion.includes('what is your name')) {
    console.log(`📛 Name question detected`);
    return "My name is MediVision. I'm your medical assistant chatbot developed by Med Amine Chouchane.";
  }

  // PRIORITY 5: Check for help/capability questions
  const helpKeywords = ['how can you help', 'what can you do', 'how can you assist', 'what help can you provide', 'what are your abilities'];
  for (const keyword of helpKeywords) {
    if (lowerQuestion.includes(keyword)) {
      console.log(`🆘 Help/capability question detected: "${keyword}"`);
      return "I'm MediVision, your medical assistant developed by Med Amine Chouchane. I can help you with: 🔹 Medicine information and identification 🔹 Symptoms and health conditions guidance 🔹 General wellness advice 🔹 Drug interactions and side effects 🔹 First aid information. What specific medical question do you have?";
    }
  }

  // PRIORITY 6: Find best match in knowledge base using improved scoring
  let bestMatch: MedicalKnowledge | null = null;
  let highestScore = 0;

  console.log(`🔍 Searching ${medicalKnowledgeBase.length} knowledge base entries...`);

  for (const item of medicalKnowledgeBase) {
    let score = 0;

    // Higher weight for identity category
    if (item.category === 'identity') {
      score += 5; // Boost identity responses
    }

    // Check keywords with higher weight
    for (const keyword of item.keywords) {
      if (lowerQuestion.includes(keyword.toLowerCase())) {
        score += 3; // Increased from 2
      }
    }

    // Check question similarity
    const questionWords = item.question.toLowerCase().split(' ');
    for (const word of questionWords) {
      if (lowerQuestion.includes(word) && word.length > 2) { // Reduced from 3
        score += 1;
      }
    }

    if (score > highestScore) {
      highestScore = score;
      bestMatch = item;
    }
  }

  console.log(`📊 Best match score: ${highestScore}, threshold: 2`);

  if (bestMatch && highestScore >= 2) {
    console.log(`✅ Knowledge base match: "${bestMatch.question}"`);
    return bestMatch.response;
  }

  // Default medical response
  console.log(`❌ No match found, using default response`);
  return errorResponses.cors;
}
