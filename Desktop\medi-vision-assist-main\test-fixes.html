<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediVision Assist - Test Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .loading { opacity: 0.6; }
    </style>
</head>
<body>
    <h1>MediVision Assist - Test Fixes</h1>
    <p>This page helps test the fixes for the chatbot server errors.</p>

    <div class="test-section info">
        <h3>Test Instructions</h3>
        <ol>
            <li>Open browser developer tools (F12)</li>
            <li>Go to the Console tab to see detailed logs</li>
            <li>Click the test buttons below</li>
            <li>Check the results and console output</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>1. Test Medical Chat Function</h3>
        <button onclick="testMedicalChat()">Test Chat Function</button>
        <div id="chat-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Profile Query (requires authentication)</h3>
        <button onclick="testProfileQuery()">Test Profile Query</button>
        <div id="profile-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Environment Variables</h3>
        <button onclick="testEnvironment()">Test Environment</button>
        <div id="env-result"></div>
    </div>

    <script type="module">
        // Import Supabase client
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        const SUPABASE_URL = "https://ygkxdctaraeragizxfbt.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Make functions available globally
        window.testMedicalChat = testMedicalChat;
        window.testProfileQuery = testProfileQuery;
        window.testEnvironment = testEnvironment;

        async function testMedicalChat() {
            const resultDiv = document.getElementById('chat-result');
            resultDiv.innerHTML = '<div class="loading">Testing medical chat function...</div>';
            
            console.log('🧪 Testing medical chat function...');
            
            try {
                const response = await supabase.functions.invoke('medical-chat', {
                    body: {
                        question: 'What is aspirin used for?',
                        medicineName: 'General Medical Assistant',
                        userId: 'test-user-' + Date.now()
                    }
                });
                
                console.log('📥 Chat response:', response);
                
                if (response.error) {
                    throw new Error(response.error.message);
                }
                
                if (response.data && response.data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p><strong>Reply:</strong> ${response.data.reply.substring(0, 200)}...</p>
                            <p><strong>Response Time:</strong> ${response.data.responseTime}ms</p>
                        </div>
                    `;
                } else {
                    throw new Error(response.data?.error || 'Unknown error');
                }
                
            } catch (error) {
                console.error('❌ Chat test failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Check the browser console for more details.</p>
                    </div>
                `;
            }
        }

        async function testProfileQuery() {
            const resultDiv = document.getElementById('profile-result');
            resultDiv.innerHTML = '<div class="loading">Testing profile query...</div>';
            
            console.log('🧪 Testing profile query...');
            
            try {
                // First check if user is authenticated
                const { data: { user } } = await supabase.auth.getUser();
                
                if (!user) {
                    resultDiv.innerHTML = `
                        <div class="info">
                            <h4>ℹ️ Not Authenticated</h4>
                            <p>You need to be logged in to test profile queries.</p>
                            <p>Go to the main app and sign in first.</p>
                        </div>
                    `;
                    return;
                }
                
                console.log('👤 Current user:', user);
                
                const { data: profile, error } = await supabase
                    .from('profiles')
                    .select('is_admin, email, full_name')
                    .eq('id', user.id)
                    .maybeSingle();
                
                console.log('📥 Profile query result:', { profile, error });
                
                if (error) {
                    throw error;
                }
                
                if (profile) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p><strong>Email:</strong> ${profile.email}</p>
                            <p><strong>Full Name:</strong> ${profile.full_name || 'Not set'}</p>
                            <p><strong>Is Admin:</strong> ${profile.is_admin ? 'Yes' : 'No'}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>⚠️ Profile Not Found</h4>
                            <p>Profile doesn't exist for user: ${user.id}</p>
                            <p>This might indicate the trigger didn't run properly.</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('❌ Profile test failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>Code:</strong> ${error.code || 'Unknown'}</p>
                        <p>Check the browser console for more details.</p>
                    </div>
                `;
            }
        }

        async function testEnvironment() {
            const resultDiv = document.getElementById('env-result');
            resultDiv.innerHTML = '<div class="loading">Testing environment...</div>';
            
            console.log('🧪 Testing environment configuration...');
            
            const tests = [
                {
                    name: 'Supabase URL',
                    value: SUPABASE_URL,
                    expected: 'https://ygkxdctaraeragizxfbt.supabase.co'
                },
                {
                    name: 'Supabase Anon Key',
                    value: SUPABASE_ANON_KEY ? 'Set (length: ' + SUPABASE_ANON_KEY.length + ')' : 'Not set',
                    expected: 'Set'
                }
            ];
            
            let allPassed = true;
            let resultsHtml = '<h4>Environment Test Results:</h4><ul>';
            
            tests.forEach(test => {
                const passed = test.value && test.value !== 'Not set';
                if (!passed) allPassed = false;
                
                resultsHtml += `
                    <li>
                        ${passed ? '✅' : '❌'} <strong>${test.name}:</strong> ${test.value}
                    </li>
                `;
            });
            
            resultsHtml += '</ul>';
            
            resultDiv.innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    ${resultsHtml}
                    ${allPassed ? '<p>All environment variables are configured correctly!</p>' : '<p>Some environment variables are missing or incorrect.</p>'}
                </div>
            `;
        }
    </script>
</body>
</html>
