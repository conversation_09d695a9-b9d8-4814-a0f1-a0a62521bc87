// Test to check if the function deployment worked
const SUPABASE_URL = "https://ygkxdctaraeragizxfbt.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc";

async function testDeploymentStatus() {
  console.log('🔍 Testing if function deployment worked...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({
        question: "deployment test",
        medicineName: "Test",
        userId: "test-user"
      })
    });

    console.log(`📡 Response status: ${response.status}`);
    
    const data = await response.json();
    console.log('📥 Response data:', JSON.stringify(data, null, 2));
    
    // Check the specific error message to understand what's happening
    if (data.error) {
      if (data.error.includes('OpenRouter API key is not configured')) {
        console.log('❌ ISSUE: Environment variables not set in function');
      } else if (data.error.includes('OpenRouter API error: 404')) {
        console.log('❌ ISSUE: Function still using old model OR API key issue');
        console.log('💡 SOLUTION: Function deployment may not have completed');
      } else if (data.error.includes('No endpoints found')) {
        console.log('❌ ISSUE: Model name is still wrong in deployed function');
      } else {
        console.log('❌ UNKNOWN ISSUE:', data.error);
      }
    }
    
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
}

testDeploymentStatus();
