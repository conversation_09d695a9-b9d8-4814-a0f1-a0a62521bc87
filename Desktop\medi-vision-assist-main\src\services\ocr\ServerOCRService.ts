/**
 * Server-side OCR service for high-performance medicine identification
 * Uses a dedicated Node.js server with Sharp image processing and worker pools
 */

export interface ServerOCRResult {
  success: boolean;
  text: string;
  confidence: number;
  processingTime: number;
  medicineNames: string[];
  words: any[];
  cached: boolean;
  error?: string;
}

export interface ServerOCRConfig {
  serverUrl?: string;
  timeout?: number;
  fallbackToClient?: boolean;
}

export class ServerOCRService {
  private config: ServerOCRConfig;
  private isServerAvailable: boolean = false;

  constructor(config: ServerOCRConfig = {}) {
    this.config = {
      serverUrl: 'http://localhost:3001',
      timeout: 30000, // 30 seconds
      fallbackToClient: true,
      ...config
    };
    
    // Check server availability on initialization
    this.checkServerHealth();
  }

  /**
   * Check if the OCR server is available
   */
  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.serverUrl}/api/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout for health check
      });
      
      if (response.ok) {
        const health = await response.json();
        this.isServerAvailable = health.status === 'healthy';
        console.log('🟢 OCR Server is available:', health);
        return true;
      } else {
        this.isServerAvailable = false;
        console.warn('🟡 OCR Server health check failed:', response.status);
        return false;
      }
    } catch (error) {
      this.isServerAvailable = false;
      console.warn('🔴 OCR Server is not available:', error);
      return false;
    }
  }

  /**
   * Process image using server-side OCR
   */
  async processImage(imageFile: File): Promise<ServerOCRResult> {
    // Check server availability first
    if (!this.isServerAvailable) {
      await this.checkServerHealth();
    }

    if (!this.isServerAvailable) {
      throw new Error('OCR Server is not available');
    }

    try {
      console.log('🚀 Sending image to server-side OCR...');
      
      // Prepare form data
      const formData = new FormData();
      formData.append('image', imageFile);

      // Send request to server
      const response = await fetch(`${this.config.serverUrl}/api/ocr/process`, {
        method: 'POST',
        body: formData,
        signal: AbortSignal.timeout(this.config.timeout!)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Server responded with ${response.status}`);
      }

      const result: ServerOCRResult = await response.json();
      
      console.log(`✅ Server OCR completed in ${result.processingTime}ms`);
      console.log(`📊 Confidence: ${result.confidence.toFixed(1)}%`);
      console.log(`💊 Medicine names found: ${result.medicineNames.join(', ')}`);
      
      return result;

    } catch (error) {
      console.error('❌ Server OCR failed:', error);
      
      // Return error result
      return {
        success: false,
        text: '',
        confidence: 0,
        processingTime: 0,
        medicineNames: [],
        words: [],
        cached: false,
        error: error instanceof Error ? error.message : 'Unknown server error'
      };
    }
  }

  /**
   * Process image with automatic fallback to client-side OCR
   * @deprecated Use processImage() directly and handle fallback logic in the calling component
   */
  async processImageWithFallback(
    imageFile: File,
    clientOCRFallback?: (file: File) => Promise<any>
  ): Promise<ServerOCRResult> {
    console.warn('⚠️ processImageWithFallback is deprecated. Use processImage() directly.');

    try {
      // Just try server-side OCR and return the result
      const serverResult = await this.processImage(imageFile);
      return serverResult;

    } catch (error) {
      console.error('❌ Server OCR failed:', error);

      return {
        success: false,
        text: '',
        confidence: 0,
        processingTime: 0,
        medicineNames: [],
        words: [],
        cached: false,
        error: error instanceof Error ? error.message : 'Server OCR failed'
      };
    }
  }

  /**
   * DEPRECATED: Legacy fallback method - use intelligent fallback in ImageUpload component instead
   */
  private async legacyProcessImageWithFallback(
    imageFile: File,
    clientOCRFallback?: (file: File) => Promise<any>
  ): Promise<ServerOCRResult> {
    try {
      // Try server-side OCR first
      const serverResult = await this.processImage(imageFile);

      if (serverResult.success && serverResult.confidence > 50) {
        return serverResult;
      }

      // If server OCR failed or low confidence, try fallback
      if (this.config.fallbackToClient && clientOCRFallback) {
        console.log('🔄 Falling back to client-side OCR...');

        try {
          const clientResult = await clientOCRFallback(imageFile);

          // Convert client result to server result format
          return {
            success: clientResult.success || false,
            text: clientResult.ocrResult?.text || clientResult.text || '',
            confidence: clientResult.ocrResult?.confidence || clientResult.confidence || 0,
            processingTime: clientResult.ocrResult?.processingTime || 0,
            medicineNames: clientResult.medicineInfo?.potentialNames || [],
            words: clientResult.ocrResult?.words || [],
            cached: false,
            error: clientResult.error
          };
        } catch (fallbackError) {
          console.error('❌ Client-side fallback also failed:', fallbackError);
          return serverResult; // Return original server result
        }
      }

      return serverResult;

    } catch (error) {
      // If server is completely unavailable, try client fallback
      if (this.config.fallbackToClient && clientOCRFallback) {
        console.log('🔄 Server unavailable, using client-side OCR...');

        try {
          const clientResult = await clientOCRFallback(imageFile);

          return {
            success: clientResult.success || false,
            text: clientResult.ocrResult?.text || clientResult.text || '',
            confidence: clientResult.ocrResult?.confidence || clientResult.confidence || 0,
            processingTime: clientResult.ocrResult?.processingTime || 0,
            medicineNames: clientResult.medicineInfo?.potentialNames || [],
            words: clientResult.ocrResult?.words || [],
            cached: false
          };
        } catch (fallbackError) {
          console.error('❌ Both server and client OCR failed:', fallbackError);
        }
      }
      
      return {
        success: false,
        text: '',
        confidence: 0,
        processingTime: 0,
        medicineNames: [],
        words: [],
        cached: false,
        error: error instanceof Error ? error.message : 'OCR processing failed'
      };
    }
  }

  /**
   * Clear server-side cache
   */
  async clearCache(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.serverUrl}/api/cache/clear`, {
        method: 'POST',
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        console.log('✅ Server cache cleared successfully');
        return true;
      } else {
        console.warn('⚠️ Failed to clear server cache:', response.status);
        return false;
      }
    } catch (error) {
      console.error('❌ Error clearing server cache:', error);
      return false;
    }
  }

  /**
   * Get server status and statistics
   */
  async getServerStatus(): Promise<any> {
    try {
      const response = await fetch(`${this.config.serverUrl}/api/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        return await response.json();
      } else {
        throw new Error(`Server responded with ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Error getting server status:', error);
      return null;
    }
  }

  /**
   * Check if server-side OCR is available
   */
  isAvailable(): boolean {
    return this.isServerAvailable;
  }

  /**
   * Update server configuration
   */
  updateConfig(newConfig: Partial<ServerOCRConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Re-check server health if URL changed
    if (newConfig.serverUrl) {
      this.checkServerHealth();
    }
  }
}
