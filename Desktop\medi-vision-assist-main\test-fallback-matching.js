/**
 * Test the fallback matching algorithm to see why identity questions aren't working
 */

// Simulate the knowledge base and matching function
const medicalKnowledgeBase = [
  {
    question: "Who created you?",
    response: "I was developed by Med <PERSON>.",
    keywords: ["created", "made", "developed", "built", "creator", "developer"],
    category: "identity"
  },
  {
    question: "Who made you?",
    response: "I was developed by Me<PERSON>.",
    keywords: ["made", "created", "developed", "built", "maker", "creator"],
    category: "identity"
  },
  {
    question: "Who developed you?",
    response: "I was developed by Med <PERSON>.",
    keywords: ["developed", "created", "made", "built", "developer", "creator"],
    category: "identity"
  },
  {
    question: "Who are you?",
    response: "I'm MediVision, your medical assistant chatbot developed by <PERSON><PERSON>. I help with medical questions and medicine identification.",
    keywords: ["who", "are", "you", "identity", "name"],
    category: "identity"
  }
];

function findBestMatch(question) {
  const lowerQuestion = question.toLowerCase();
  console.log(`\n🔍 Testing: "${question}"`);
  console.log(`📝 Lowercase: "${lowerQuestion}"`);
  
  // Check for greetings first
  const greetingKeywords = ['hi', 'hello', 'hey', 'greetings', 'can you talk', 'are you there'];
  if (greetingKeywords.some(keyword => lowerQuestion.includes(keyword))) {
    console.log(`👋 Matched greeting keyword`);
    return "Hello! I'm MediVision, your medical assistant chatbot. How can I help you with your health questions today?";
  }
  
  // Find best match in knowledge base
  let bestMatch = null;
  let highestScore = 0;
  
  console.log(`🔍 Checking ${medicalKnowledgeBase.length} knowledge base entries...`);
  
  for (const item of medicalKnowledgeBase) {
    let score = 0;
    console.log(`\n  📋 Checking: "${item.question}"`);
    console.log(`  🏷️ Keywords: [${item.keywords.join(', ')}]`);
    
    // Check keywords
    for (const keyword of item.keywords) {
      if (lowerQuestion.includes(keyword.toLowerCase())) {
        score += 2;
        console.log(`    ✅ Keyword match: "${keyword}" (+2) = ${score}`);
      }
    }
    
    // Check question similarity
    const questionWords = item.question.toLowerCase().split(' ');
    console.log(`  📝 Question words: [${questionWords.join(', ')}]`);
    
    for (const word of questionWords) {
      if (lowerQuestion.includes(word) && word.length > 3) {
        score += 1;
        console.log(`    ✅ Word match: "${word}" (+1) = ${score}`);
      }
    }
    
    console.log(`  📊 Final score: ${score}`);
    
    if (score > highestScore) {
      highestScore = score;
      bestMatch = item;
      console.log(`  🎯 New best match! Score: ${score}`);
    }
  }
  
  console.log(`\n📊 Best match score: ${highestScore}`);
  console.log(`🎯 Threshold: >= 2`);
  
  if (bestMatch && highestScore >= 2) {
    console.log(`✅ Match found: "${bestMatch.question}"`);
    console.log(`💬 Response: "${bestMatch.response}"`);
    return bestMatch.response;
  }
  
  console.log(`❌ No match found (score ${highestScore} < 2)`);
  return "I'm MediVision, your medical assistant. I can help with questions about medicines, health conditions, symptoms, and general wellness. Please ask me a specific medical question, and I'll do my best to help. Remember to consult healthcare professionals for personalized medical advice.";
}

// Test the problematic questions
const testQuestions = [
  'who create you',      // Missing 'd' - this is what user typed
  'who created you',     // Correct spelling
  'who made you',
  'who developed you',
  'who are you',
  'who is your creator',
  'what is your name'
];

console.log('🚀 Testing Fallback Matching Algorithm');
console.log('=' .repeat(60));

for (const question of testQuestions) {
  const result = findBestMatch(question);
  console.log(`\n${'='.repeat(50)}`);
}

console.log('\n📋 ANALYSIS:');
console.log('If identity questions are not matching, we need to:');
console.log('1. Add more keywords to catch variations');
console.log('2. Lower the threshold or improve scoring');
console.log('3. Add specific identity detection logic');
