// Test with a real medical question
const SUPABASE_URL = "https://ygkxdctaraeragizxfbt.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc";

async function testRealMedicalQuestion() {
  console.log('🧪 Testing with a real medical question...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({
        question: "What is ibuprofen used for?",
        medicineName: "Ibuprofen",
        userId: "fc37eb65-c52a-4e9e-a718-f2d2b4d8d324"
      })
    });

    console.log(`📡 Response status: ${response.status}`);
    
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ CHATBOT IS WORKING PERFECTLY!');
      console.log('💬 AI Response:', data.reply);
      console.log('⏱️ Response time:', data.responseTime + 'ms');
      console.log('🆔 Session ID:', data.sessionId);
    } else {
      console.log('❌ Error:', data.error);
    }
    
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
}

testRealMedicalQuestion();
