import { supabase } from '@/integrations/supabase/client';
import { OCRScanRecord } from '@/types/ocr';

/**
 * Service for managing OCR scan records in Supabase
 */
export class OCRScanService {
  /**
   * Store an OCR scan result in the database
   */
  static async createScanRecord(record: Omit<OCRScanRecord, 'id' | 'created_at'>): Promise<OCRScanRecord | null> {
    try {
      const { data, error } = await supabase
        .from('medicine_scans')
        .insert([record])
        .select()
        .single();

      if (error) {
        console.error('❌ Failed to create OCR scan record:', error);
        throw error;
      }

      console.log('✅ OCR scan record created successfully:', data.id);
      return data;
    } catch (error) {
      console.error('❌ Error creating OCR scan record:', error);
      return null;
    }
  }

  /**
   * Update an existing OCR scan record
   */
  static async updateScanRecord(id: string, updates: Partial<OCRScanRecord>): Promise<OCRScanRecord | null> {
    try {
      const { data, error } = await supabase
        .from('medicine_scans')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Failed to update OCR scan record:', error);
        throw error;
      }

      console.log('✅ OCR scan record updated successfully:', id);
      return data;
    } catch (error) {
      console.error('❌ Error updating OCR scan record:', error);
      return null;
    }
  }

  /**
   * Get OCR scan records for a user
   */
  static async getUserScanRecords(userId: string, limit: number = 50): Promise<OCRScanRecord[]> {
    try {
      const { data, error } = await supabase
        .from('medicine_scans')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Failed to fetch user scan records:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error fetching user scan records:', error);
      return [];
    }
  }

  /**
   * Get OCR scan statistics for a user
   */
  static async getUserScanStats(userId: string): Promise<{
    totalScans: number;
    successfulScans: number;
    failedScans: number;
    averageConfidence: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('medicine_scans')
        .select('scan_status, confidence_score')
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Failed to fetch user scan stats:', error);
        throw error;
      }

      const records = data || [];
      const totalScans = records.length;
      const successfulScans = records.filter(r => r.scan_status === 'completed').length;
      const failedScans = records.filter(r => r.scan_status === 'failed').length;

      const confidenceScores = records
        .filter(r => r.confidence_score > 0)
        .map(r => r.confidence_score);

      const averageConfidence = confidenceScores.length > 0
        ? confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length
        : 0;

      return {
        totalScans,
        successfulScans,
        failedScans,
        averageConfidence: Math.round(averageConfidence),
      };
    } catch (error) {
      console.error('❌ Error calculating user scan stats:', error);
      return {
        totalScans: 0,
        successfulScans: 0,
        failedScans: 0,
        averageConfidence: 0,
      };
    }
  }

  /**
   * Delete an OCR scan record
   */
  static async deleteScanRecord(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('medicine_scans')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Failed to delete OCR scan record:', error);
        throw error;
      }

      console.log('✅ OCR scan record deleted successfully:', id);
      return true;
    } catch (error) {
      console.error('❌ Error deleting OCR scan record:', error);
      return false;
    }
  }

  /**
   * Get recent successful medicine identifications
   */
  static async getRecentSuccessfulScans(limit: number = 10): Promise<OCRScanRecord[]> {
    try {
      const { data, error } = await supabase
        .from('medicine_scans')
        .select('*')
        .eq('scan_status', 'completed')
        .not('medicine_name', 'is', null)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Failed to fetch recent successful scans:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error fetching recent successful scans:', error);
      return [];
    }
  }

  /**
   * Search OCR scan records by medicine name
   */
  static async searchScansByMedicine(medicineName: string, userId?: string): Promise<OCRScanRecord[]> {
    try {
      let query = supabase
        .from('medicine_scans')
        .select('*')
        .ilike('medicine_name', `%${medicineName}%`)
        .eq('scan_status', 'completed')
        .order('created_at', { ascending: false });

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Failed to search scans by medicine:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error searching scans by medicine:', error);
      return [];
    }
  }
}
