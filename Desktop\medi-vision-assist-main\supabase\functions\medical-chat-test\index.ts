import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  console.log('🧪 Test function called')

  try {
    const requestBody = await req.json()
    const { question } = requestBody

    console.log('📥 Question received:', question)

    // Simple fallback response
    const testReply = "Hello! I'm MediVision, your medical assistant chatbot. This is a test response from the fallback system. How can I help you with your medical questions today?"

    return new Response(
      JSON.stringify({
        success: true,
        reply: testReply,
        source: 'test-fallback',
        note: 'This is a test function to verify fallback system'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('❌ Test function error:', error)

    return new Response(
      JSON.stringify({
        success: true,
        reply: "Test fallback response - even errors are handled!",
        source: 'test-error-fallback',
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/medical-chat-test' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
