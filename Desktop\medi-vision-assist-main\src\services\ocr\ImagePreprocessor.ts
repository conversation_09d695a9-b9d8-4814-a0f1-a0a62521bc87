/**
 * Enhanced image preprocessing service for medicine packaging OCR
 * Implements automatic rotation correction, contrast/brightness adjustment,
 * noise reduction, resolution enhancement, and medicine-specific optimizations
 */
export class ImagePreprocessor {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private debugMode: boolean = false;
  private debugImages: { name: string; dataUrl: string }[] = [];

  constructor(debugMode: boolean = false) {
    this.canvas = document.createElement('canvas');
    const context = this.canvas.getContext('2d');
    if (!context) {
      throw new Error('Failed to get 2D context from canvas');
    }
    this.ctx = context;
    this.debugMode = debugMode;
  }

  /**
   * Enable debug mode to capture intermediate processing steps
   */
  enableDebugMode(): void {
    this.debugMode = true;
    this.debugImages = [];
  }

  /**
   * Get debug images captured during processing
   */
  getDebugImages(): { name: string; dataUrl: string }[] {
    return this.debugImages;
  }

  /**
   * Enhanced preprocessing pipeline optimized for medicine packaging
   * Implements MANDATORY UPSCALING and INTELLIGENT ROI DETECTION
   */
  async enhanceImage(imageFile: File): Promise<File> {
    try {
      console.log('🔄 Starting enhanced image preprocessing pipeline...');

      // Load image
      const img = await this.loadImage(imageFile);

      // CRITICAL FIX 1: Mandatory upscaling check
      const originalSize = { width: img.width, height: img.height };
      const minDimension = Math.min(img.width, img.height);

      if (minDimension < 600) {
        console.log(`⚠️ Image too small for OCR (${minDimension}px). Mandatory upscaling required.`);
      }

      // Analyze image characteristics for adaptive processing
      const imageAnalysis = this.analyzeImage(img);
      console.log('📊 Image analysis:', imageAnalysis);

      // CRITICAL FIX 2: Intelligent ROI detection and cropping
      const roiResult = await this.detectAndCropROI(img);
      const processedImg = roiResult.croppedImage || img;

      if (roiResult.croppedImage) {
        console.log(`✂️ ROI detected and cropped: ${roiResult.confidence}% confidence`);
        console.log(`📏 Original: ${img.width}x${img.height} → ROI: ${processedImg.width}x${processedImg.height}`);
      }

      // CRITICAL FIX 3: Aggressive upscaling with high-quality interpolation
      const optimalSize = this.calculateOptimalSizeWithMandatoryUpscaling(
        processedImg.width,
        processedImg.height,
        originalSize
      );

      this.canvas.width = optimalSize.width;
      this.canvas.height = optimalSize.height;

      // Use Lanczos-like high-quality scaling
      this.ctx.imageSmoothingEnabled = true;
      this.ctx.imageSmoothingQuality = 'high';
      this.ctx.drawImage(processedImg, 0, 0, optimalSize.width, optimalSize.height);

      if (this.debugMode) {
        this.captureDebugImage('01-upscaled-roi');
      }

      // Apply adaptive preprocessing steps based on image analysis
      if (imageAnalysis.needsRotationCorrection) {
        await this.correctRotation();
        if (this.debugMode) this.captureDebugImage('02-rotation-corrected');
      }

      // Medicine-specific contrast and brightness adjustments
      const contrastFactor = this.calculateOptimalContrast(imageAnalysis);
      const brightnessFactor = this.calculateOptimalBrightness(imageAnalysis);

      this.adjustContrast(contrastFactor);
      if (this.debugMode) this.captureDebugImage('03-contrast-adjusted');

      this.adjustBrightness(brightnessFactor);
      if (this.debugMode) this.captureDebugImage('04-brightness-adjusted');

      // Advanced noise reduction for medicine packaging
      if (imageAnalysis.hasNoise) {
        this.advancedNoiseReduction();
        if (this.debugMode) this.captureDebugImage('05-noise-reduced');
      }

      // Text-optimized sharpening
      this.medicineTextSharpening(imageAnalysis);
      if (this.debugMode) this.captureDebugImage('06-sharpened');

      // Final text enhancement
      this.enhanceTextRegions();
      if (this.debugMode) this.captureDebugImage('07-text-enhanced');

      // Convert back to File
      const enhancedFile = await this.canvasToFile(imageFile.name);
      console.log('✅ Enhanced image preprocessing completed');

      if (this.debugMode) {
        console.log('🐛 Debug images captured:', this.debugImages.length);
      }

      return enhancedFile;
    } catch (error) {
      console.error('❌ Image preprocessing failed:', error);
      // Return original file if preprocessing fails
      return imageFile;
    }
  }

  /**
   * Analyze image characteristics for adaptive processing
   */
  private analyzeImage(img: HTMLImageElement): {
    width: number;
    height: number;
    aspectRatio: number;
    isLowResolution: boolean;
    hasNoise: boolean;
    needsRotationCorrection: boolean;
    brightness: number;
    contrast: number;
  } {
    // Create temporary canvas for analysis
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d')!;
    tempCanvas.width = img.width;
    tempCanvas.height = img.height;
    tempCtx.drawImage(img, 0, 0);

    const imageData = tempCtx.getImageData(0, 0, img.width, img.height);
    const data = imageData.data;

    // Calculate brightness and contrast
    let totalBrightness = 0;
    let brightnessValues: number[] = [];

    for (let i = 0; i < data.length; i += 4) {
      const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3;
      totalBrightness += brightness;
      brightnessValues.push(brightness);
    }

    const avgBrightness = totalBrightness / (data.length / 4);

    // Calculate contrast (standard deviation of brightness)
    const variance = brightnessValues.reduce((sum, brightness) =>
      sum + Math.pow(brightness - avgBrightness, 2), 0) / brightnessValues.length;
    const contrast = Math.sqrt(variance);

    return {
      width: img.width,
      height: img.height,
      aspectRatio: img.width / img.height,
      isLowResolution: img.width < 800 || img.height < 600,
      hasNoise: contrast < 30, // Low contrast often indicates noise
      needsRotationCorrection: this.detectRotationNeeded(imageData),
      brightness: avgBrightness,
      contrast: contrast
    };
  }

  /**
   * CRITICAL FIX: Detect and crop Region of Interest (ROI) for text-heavy areas
   */
  private async detectAndCropROI(img: HTMLImageElement): Promise<{
    croppedImage: HTMLImageElement | null;
    confidence: number;
    boundingBox?: { x: number; y: number; width: number; height: number };
  }> {
    try {
      // Create temporary canvas for analysis
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d')!;
      tempCanvas.width = img.width;
      tempCanvas.height = img.height;
      tempCtx.drawImage(img, 0, 0);

      const imageData = tempCtx.getImageData(0, 0, img.width, img.height);
      const { width, height, data } = imageData;

      // Convert to grayscale and detect edges
      const grayData = new Uint8Array(width * height);
      for (let i = 0; i < data.length; i += 4) {
        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
        grayData[i / 4] = gray;
      }

      // Apply Sobel edge detection to find text regions
      const edgeData = this.applySobelEdgeDetection(grayData, width, height);

      // Find the bounding box of the most text-dense region
      const textRegions = this.findTextRegions(edgeData, width, height);

      if (textRegions.length === 0) {
        console.log('⚠️ No text regions detected, using full image');
        return { croppedImage: null, confidence: 0 };
      }

      // Select the largest and most central text region
      const bestRegion = this.selectBestTextRegion(textRegions, width, height);

      // Add padding around the detected region
      const padding = Math.min(width, height) * 0.1; // 10% padding
      const cropBox = {
        x: Math.max(0, bestRegion.x - padding),
        y: Math.max(0, bestRegion.y - padding),
        width: Math.min(width - bestRegion.x + padding, bestRegion.width + 2 * padding),
        height: Math.min(height - bestRegion.y + padding, bestRegion.height + 2 * padding)
      };

      // Create cropped image
      const croppedCanvas = document.createElement('canvas');
      const croppedCtx = croppedCanvas.getContext('2d')!;
      croppedCanvas.width = cropBox.width;
      croppedCanvas.height = cropBox.height;

      croppedCtx.drawImage(
        img,
        cropBox.x, cropBox.y, cropBox.width, cropBox.height,
        0, 0, cropBox.width, cropBox.height
      );

      // Convert to HTMLImageElement
      const croppedImage = await this.canvasToImage(croppedCanvas);

      const confidence = this.calculateROIConfidence(bestRegion, width, height);

      return {
        croppedImage,
        confidence,
        boundingBox: cropBox
      };

    } catch (error) {
      console.error('❌ ROI detection failed:', error);
      return { croppedImage: null, confidence: 0 };
    }
  }

  /**
   * CRITICAL FIX: Calculate optimal size with MANDATORY upscaling
   */
  private calculateOptimalSizeWithMandatoryUpscaling(
    width: number,
    height: number,
    originalSize: { width: number; height: number }
  ): { width: number; height: number } {
    // MANDATORY minimum dimensions for Tesseract OCR
    const absoluteMinWidth = 800;
    const absoluteMinHeight = 600;
    const optimalMinWidth = 1600;  // Increased from 1200
    const optimalMinHeight = 1200; // Increased from 800
    const maxWidth = 3200;         // Increased from 2400
    const maxHeight = 2400;        // Increased from 1800

    let newWidth = width;
    let newHeight = height;
    let scaleFactor = 1;

    // CRITICAL: Check if image is dangerously small
    const minDimension = Math.min(width, height);
    if (minDimension < absoluteMinWidth) {
      console.log(`🚨 CRITICAL: Image too small (${minDimension}px). Applying emergency upscaling.`);
      scaleFactor = absoluteMinWidth / minDimension;
      newWidth = Math.round(width * scaleFactor);
      newHeight = Math.round(height * scaleFactor);
    }

    // Apply optimal upscaling if still below optimal size
    if (newWidth < optimalMinWidth || newHeight < optimalMinHeight) {
      const scaleX = optimalMinWidth / newWidth;
      const scaleY = optimalMinHeight / newHeight;
      const optimalScale = Math.max(scaleX, scaleY);

      // Use high-quality upscaling factor (2x or 3x for best results)
      const finalScale = Math.max(2, Math.ceil(optimalScale));

      newWidth = Math.round(newWidth * finalScale);
      newHeight = Math.round(newHeight * finalScale);

      console.log(`📈 Applying ${finalScale}x upscaling: ${width}x${height} → ${newWidth}x${newHeight}`);
    }

    // Prevent excessive upscaling
    if (newWidth > maxWidth || newHeight > maxHeight) {
      const scaleX = maxWidth / newWidth;
      const scaleY = maxHeight / newHeight;
      const scale = Math.min(scaleX, scaleY);

      newWidth = Math.round(newWidth * scale);
      newHeight = Math.round(newHeight * scale);

      console.log(`📉 Capping size to prevent excessive upscaling: ${newWidth}x${newHeight}`);
    }

    return { width: newWidth, height: newHeight };
  }

  /**
   * Calculate optimal contrast factor based on image analysis
   */
  private calculateOptimalContrast(analysis: any): number {
    if (analysis.contrast < 20) {
      return 1.8; // High contrast boost for very low contrast images
    } else if (analysis.contrast < 40) {
      return 1.5; // Medium contrast boost
    } else if (analysis.contrast > 80) {
      return 1.1; // Minimal boost for high contrast images
    }
    return 1.3; // Default boost
  }

  /**
   * Calculate optimal brightness factor based on image analysis
   */
  private calculateOptimalBrightness(analysis: any): number {
    if (analysis.brightness < 80) {
      return 25; // Significant brightness boost for dark images
    } else if (analysis.brightness < 120) {
      return 15; // Medium brightness boost
    } else if (analysis.brightness > 200) {
      return -10; // Slight reduction for very bright images
    }
    return 10; // Default boost
  }

  /**
   * Detect if rotation correction is needed
   */
  private detectRotationNeeded(imageData: ImageData): boolean {
    // Simple heuristic: check for horizontal/vertical line patterns
    // This is a simplified implementation - could be enhanced with more sophisticated algorithms
    const { width, height, data } = imageData;

    let horizontalLines = 0;
    let verticalLines = 0;

    // Sample every 10th row/column for performance
    for (let y = 0; y < height; y += 10) {
      let horizontalEdges = 0;
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        const prevIdx = (y * width + (x - 1)) * 4;
        const nextIdx = (y * width + (x + 1)) * 4;

        const current = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        const prev = (data[prevIdx] + data[prevIdx + 1] + data[prevIdx + 2]) / 3;
        const next = (data[nextIdx] + data[nextIdx + 1] + data[nextIdx + 2]) / 3;

        if (Math.abs(current - prev) > 30 || Math.abs(current - next) > 30) {
          horizontalEdges++;
        }
      }
      if (horizontalEdges > width * 0.3) horizontalLines++;
    }

    for (let x = 0; x < width; x += 10) {
      let verticalEdges = 0;
      for (let y = 1; y < height - 1; y++) {
        const idx = (y * width + x) * 4;
        const prevIdx = ((y - 1) * width + x) * 4;
        const nextIdx = ((y + 1) * width + x) * 4;

        const current = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        const prev = (data[prevIdx] + data[prevIdx + 1] + data[prevIdx + 2]) / 3;
        const next = (data[nextIdx] + data[nextIdx + 1] + data[nextIdx + 2]) / 3;

        if (Math.abs(current - prev) > 30 || Math.abs(current - next) > 30) {
          verticalEdges++;
        }
      }
      if (verticalEdges > height * 0.3) verticalLines++;
    }

    // If there's a significant imbalance, rotation might be needed
    const ratio = Math.abs(horizontalLines - verticalLines) / Math.max(horizontalLines, verticalLines, 1);
    return ratio > 0.5;
  }

  /**
   * Advanced noise reduction specifically for medicine packaging
   */
  private advancedNoiseReduction(): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    const width = this.canvas.width;
    const height = this.canvas.height;

    // Apply median filter for noise reduction
    const newData = new Uint8ClampedArray(data);

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        for (let c = 0; c < 3; c++) { // RGB channels
          const neighbors = [];

          // Collect 3x3 neighborhood
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              const idx = ((y + dy) * width + (x + dx)) * 4 + c;
              neighbors.push(data[idx]);
            }
          }

          // Apply median filter
          neighbors.sort((a, b) => a - b);
          const median = neighbors[Math.floor(neighbors.length / 2)];

          const idx = (y * width + x) * 4 + c;
          newData[idx] = median;
        }
      }
    }

    // Apply the filtered data
    const filteredImageData = new ImageData(newData, width, height);
    this.ctx.putImageData(filteredImageData, 0, 0);
  }

  /**
   * Medicine-specific text sharpening
   */
  private medicineTextSharpening(analysis: any): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    const width = this.canvas.width;
    const height = this.canvas.height;

    // Adaptive sharpening kernel based on image characteristics
    let kernel: number[][];
    if (analysis.isLowResolution) {
      // Stronger sharpening for low resolution images
      kernel = [
        [-1, -1, -1],
        [-1,  9, -1],
        [-1, -1, -1]
      ];
    } else {
      // Moderate sharpening for normal resolution
      kernel = [
        [ 0, -1,  0],
        [-1,  5, -1],
        [ 0, -1,  0]
      ];
    }

    const newData = new Uint8ClampedArray(data);

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        for (let c = 0; c < 3; c++) { // RGB channels
          let sum = 0;

          // Apply convolution
          for (let ky = 0; ky < 3; ky++) {
            for (let kx = 0; kx < 3; kx++) {
              const pixelY = y + ky - 1;
              const pixelX = x + kx - 1;
              const idx = (pixelY * width + pixelX) * 4 + c;
              sum += data[idx] * kernel[ky][kx];
            }
          }

          const idx = (y * width + x) * 4 + c;
          newData[idx] = Math.max(0, Math.min(255, sum));
        }
      }
    }

    const sharpenedImageData = new ImageData(newData, width, height);
    this.ctx.putImageData(sharpenedImageData, 0, 0);
  }

  /**
   * Enhance text regions specifically
   */
  private enhanceTextRegions(): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;

    // Apply adaptive thresholding to enhance text
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      // Convert to grayscale
      const gray = 0.299 * r + 0.587 * g + 0.114 * b;

      // Apply adaptive threshold
      let threshold = 128;
      if (gray < 100) {
        threshold = 80; // Lower threshold for dark text
      } else if (gray > 180) {
        threshold = 200; // Higher threshold for light backgrounds
      }

      const enhanced = gray > threshold ? 255 : 0;

      // Apply enhancement while preserving some color information
      const factor = enhanced / 255;
      data[i] = Math.round(r * factor + enhanced * 0.3);
      data[i + 1] = Math.round(g * factor + enhanced * 0.3);
      data[i + 2] = Math.round(b * factor + enhanced * 0.3);
    }

    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Capture debug image for analysis
   */
  private captureDebugImage(stepName: string): void {
    if (!this.debugMode) return;

    try {
      const dataUrl = this.canvas.toDataURL('image/png');
      this.debugImages.push({
        name: stepName,
        dataUrl: dataUrl
      });
    } catch (error) {
      console.warn('Failed to capture debug image:', error);
    }
  }

  /**
   * Load image from File object
   */
  private loadImage(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Detect and correct image rotation
   * Uses edge detection to find text orientation
   */
  private async correctRotation(): Promise<void> {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const rotationAngle = this.detectRotation(imageData);
    
    if (Math.abs(rotationAngle) > 2) { // Only rotate if angle is significant
      console.log(`🔄 Correcting rotation by ${rotationAngle.toFixed(1)} degrees`);
      this.rotateCanvas(rotationAngle);
    }
  }

  /**
   * Detect rotation angle using edge detection
   */
  private detectRotation(imageData: ImageData): number {
    // Simplified rotation detection - in production, use more sophisticated algorithms
    // For now, return 0 (no rotation) - can be enhanced with Hough transform
    return 0;
  }

  /**
   * Rotate canvas by given angle
   */
  private rotateCanvas(angle: number): void {
    const radians = (angle * Math.PI) / 180;
    const cos = Math.cos(radians);
    const sin = Math.sin(radians);
    
    // Calculate new canvas dimensions
    const newWidth = Math.abs(this.canvas.width * cos) + Math.abs(this.canvas.height * sin);
    const newHeight = Math.abs(this.canvas.width * sin) + Math.abs(this.canvas.height * cos);
    
    // Create temporary canvas with current image
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d')!;
    tempCanvas.width = this.canvas.width;
    tempCanvas.height = this.canvas.height;
    tempCtx.drawImage(this.canvas, 0, 0);
    
    // Resize main canvas
    this.canvas.width = newWidth;
    this.canvas.height = newHeight;
    
    // Apply rotation
    this.ctx.translate(newWidth / 2, newHeight / 2);
    this.ctx.rotate(radians);
    this.ctx.drawImage(tempCanvas, -tempCanvas.width / 2, -tempCanvas.height / 2);
    this.ctx.setTransform(1, 0, 0, 1, 0, 0); // Reset transform
  }

  /**
   * Adjust image contrast
   */
  private adjustContrast(factor: number): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
      // Apply contrast to RGB channels
      data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));     // Red
      data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128)); // Green
      data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128)); // Blue
    }
    
    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Adjust image brightness
   */
  private adjustBrightness(adjustment: number): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
      // Apply brightness to RGB channels
      data[i] = Math.min(255, Math.max(0, data[i] + adjustment));     // Red
      data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + adjustment)); // Green
      data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + adjustment)); // Blue
    }
    
    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Reduce image noise using a simple blur filter
   */
  private reduceNoise(): void {
    // Apply a slight blur to reduce noise
    this.ctx.filter = 'blur(0.5px)';
    this.ctx.drawImage(this.canvas, 0, 0);
    this.ctx.filter = 'none'; // Reset filter
  }

  /**
   * Sharpen image to improve text clarity
   */
  private sharpenImage(): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    const width = this.canvas.width;
    const height = this.canvas.height;
    
    // Sharpening kernel
    const kernel = [
      0, -1, 0,
      -1, 5, -1,
      0, -1, 0
    ];
    
    const output = new Uint8ClampedArray(data.length);
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        for (let c = 0; c < 3; c++) { // RGB channels only
          let sum = 0;
          for (let ky = -1; ky <= 1; ky++) {
            for (let kx = -1; kx <= 1; kx++) {
              const idx = ((y + ky) * width + (x + kx)) * 4 + c;
              const kernelIdx = (ky + 1) * 3 + (kx + 1);
              sum += data[idx] * kernel[kernelIdx];
            }
          }
          const outputIdx = (y * width + x) * 4 + c;
          output[outputIdx] = Math.min(255, Math.max(0, sum));
        }
        // Copy alpha channel
        const alphaIdx = (y * width + x) * 4 + 3;
        output[alphaIdx] = data[alphaIdx];
      }
    }
    
    // Copy processed data back
    for (let i = 0; i < data.length; i++) {
      data[i] = output[i] || data[i];
    }
    
    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Convert canvas to File object
   */
  private canvasToFile(originalName: string): Promise<File> {
    return new Promise((resolve, reject) => {
      this.canvas.toBlob((blob) => {
        if (blob) {
          const enhancedFile = new File([blob], `enhanced_${originalName}`, {
            type: 'image/png'
          });
          resolve(enhancedFile);
        } else {
          reject(new Error('Failed to convert canvas to blob'));
        }
      }, 'image/png', 0.95); // High quality PNG
    });
  }

  /**
   * Apply Sobel edge detection to find text regions
   */
  private applySobelEdgeDetection(grayData: Uint8Array, width: number, height: number): Uint8Array {
    const edgeData = new Uint8Array(width * height);

    // Sobel kernels
    const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
    const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let gx = 0, gy = 0;

        // Apply Sobel kernels
        for (let ky = 0; ky < 3; ky++) {
          for (let kx = 0; kx < 3; kx++) {
            const pixelIndex = (y + ky - 1) * width + (x + kx - 1);
            const kernelIndex = ky * 3 + kx;
            const pixelValue = grayData[pixelIndex];

            gx += pixelValue * sobelX[kernelIndex];
            gy += pixelValue * sobelY[kernelIndex];
          }
        }

        // Calculate gradient magnitude
        const magnitude = Math.sqrt(gx * gx + gy * gy);
        edgeData[y * width + x] = Math.min(255, magnitude);
      }
    }

    return edgeData;
  }

  /**
   * Find text regions using connected component analysis
   */
  private findTextRegions(edgeData: Uint8Array, width: number, height: number): Array<{
    x: number; y: number; width: number; height: number; density: number;
  }> {
    const threshold = 50; // Edge threshold
    const minRegionSize = (width * height) * 0.01; // Minimum 1% of image
    const regions: Array<{ x: number; y: number; width: number; height: number; density: number }> = [];

    // Create binary edge map
    const binaryEdges = new Uint8Array(width * height);
    for (let i = 0; i < edgeData.length; i++) {
      binaryEdges[i] = edgeData[i] > threshold ? 1 : 0;
    }

    // Find connected components (simplified version)
    const visited = new Uint8Array(width * height);

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = y * width + x;

        if (binaryEdges[index] === 1 && visited[index] === 0) {
          const region = this.floodFillRegion(binaryEdges, visited, x, y, width, height);

          if (region.pixels.length > minRegionSize) {
            const bounds = this.calculateRegionBounds(region.pixels);
            const density = region.pixels.length / ((bounds.maxX - bounds.minX + 1) * (bounds.maxY - bounds.minY + 1));

            regions.push({
              x: bounds.minX,
              y: bounds.minY,
              width: bounds.maxX - bounds.minX + 1,
              height: bounds.maxY - bounds.minY + 1,
              density
            });
          }
        }
      }
    }

    return regions;
  }

  /**
   * Flood fill to find connected region
   */
  private floodFillRegion(
    binaryEdges: Uint8Array,
    visited: Uint8Array,
    startX: number,
    startY: number,
    width: number,
    height: number
  ): { pixels: Array<{ x: number; y: number }> } {
    const pixels: Array<{ x: number; y: number }> = [];
    const stack: Array<{ x: number; y: number }> = [{ x: startX, y: startY }];

    while (stack.length > 0) {
      const { x, y } = stack.pop()!;
      const index = y * width + x;

      if (x < 0 || x >= width || y < 0 || y >= height || visited[index] === 1 || binaryEdges[index] === 0) {
        continue;
      }

      visited[index] = 1;
      pixels.push({ x, y });

      // Add neighbors (4-connectivity)
      stack.push({ x: x + 1, y });
      stack.push({ x: x - 1, y });
      stack.push({ x, y: y + 1 });
      stack.push({ x, y: y - 1 });
    }

    return { pixels };
  }

  /**
   * Calculate bounding box of region pixels
   */
  private calculateRegionBounds(pixels: Array<{ x: number; y: number }>): {
    minX: number; minY: number; maxX: number; maxY: number;
  } {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (const pixel of pixels) {
      minX = Math.min(minX, pixel.x);
      minY = Math.min(minY, pixel.y);
      maxX = Math.max(maxX, pixel.x);
      maxY = Math.max(maxY, pixel.y);
    }

    return { minX, minY, maxX, maxY };
  }

  /**
   * Select the best text region based on size, position, and density
   */
  private selectBestTextRegion(
    regions: Array<{ x: number; y: number; width: number; height: number; density: number }>,
    imageWidth: number,
    imageHeight: number
  ): { x: number; y: number; width: number; height: number; density: number } {
    let bestRegion = regions[0];
    let bestScore = 0;

    for (const region of regions) {
      // Calculate score based on multiple factors
      const sizeScore = (region.width * region.height) / (imageWidth * imageHeight); // Relative size
      const centralityScore = 1 - Math.abs((region.x + region.width / 2) / imageWidth - 0.5); // How central
      const densityScore = Math.min(1, region.density * 2); // Edge density
      const aspectScore = Math.min(region.width / region.height, region.height / region.width); // Reasonable aspect ratio

      const totalScore = sizeScore * 0.3 + centralityScore * 0.2 + densityScore * 0.4 + aspectScore * 0.1;

      if (totalScore > bestScore) {
        bestScore = totalScore;
        bestRegion = region;
      }
    }

    return bestRegion;
  }

  /**
   * Calculate confidence score for ROI detection
   */
  private calculateROIConfidence(
    region: { x: number; y: number; width: number; height: number; density: number },
    imageWidth: number,
    imageHeight: number
  ): number {
    const sizeRatio = (region.width * region.height) / (imageWidth * imageHeight);
    const densityScore = Math.min(1, region.density * 2) * 100;
    const sizeScore = Math.min(1, sizeRatio * 3) * 100; // Prefer regions that are 30%+ of image

    return Math.round((densityScore + sizeScore) / 2);
  }

  /**
   * Convert canvas to HTMLImageElement
   */
  private canvasToImage(canvas: HTMLCanvasElement): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error('Failed to convert canvas to blob'));
          return;
        }

        const img = new Image();
        img.onload = () => {
          URL.revokeObjectURL(img.src);
          resolve(img);
        };
        img.onerror = reject;
        img.src = URL.createObjectURL(blob);
      }, 'image/png');
    });
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    // Canvas cleanup is handled by garbage collection
    // This method is for future resource cleanup if needed
  }
}
