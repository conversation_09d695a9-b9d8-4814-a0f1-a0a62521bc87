/**
 * Test script to verify database schema and populate with test data
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with correct credentials
const supabaseUrl = 'https://ygkxdctaraeragizxfbt.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlna3hkY3RhcmFlcmFnaXp4ZmJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNDU3MTksImV4cCI6MjA2NTgyMTcxOX0.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseSchema() {
  console.log('🔍 Testing database schema...');
  
  try {
    // Test 1: Check if medicines table exists
    console.log('\n1. Testing medicines table...');
    const { data: medicines, error: medicinesError } = await supabase
      .from('medicines')
      .select('id, generic_name, brand_names')
      .limit(5);
    
    if (medicinesError) {
      console.error('❌ Medicines table error:', medicinesError);
      return false;
    }
    
    console.log(`✅ Medicines table exists with ${medicines?.length || 0} records`);
    if (medicines && medicines.length > 0) {
      console.log('📋 Sample medicines:', medicines.map(m => `${m.generic_name} (${m.brand_names?.join(', ') || 'no brands'})`));
    }
    
    // Test 2: Check search_medicines function
    console.log('\n2. Testing search_medicines function...');
    const { data: searchResults, error: searchError } = await supabase
      .rpc('search_medicines', {
        search_term: 'panadol',
        limit_count: 3,
        similarity_threshold: 0.3
      });
    
    if (searchError) {
      console.error('❌ Search function error:', searchError);
      return false;
    }
    
    console.log(`✅ Search function works, found ${searchResults?.length || 0} results for "panadol"`);
    if (searchResults && searchResults.length > 0) {
      console.log('🔍 Search results:', searchResults.map(r => 
        `${r.generic_name} (${r.similarity_score?.toFixed(2)} similarity)`
      ));
    }
    
    // Test 3: Check medicine_scans table
    console.log('\n3. Testing medicine_scans table...');
    const { data: scans, error: scansError } = await supabase
      .from('medicine_scans')
      .select('id, medicine_name, confidence_score')
      .limit(3);
    
    if (scansError) {
      console.error('❌ Medicine scans table error:', scansError);
      return false;
    }
    
    console.log(`✅ Medicine scans table exists with ${scans?.length || 0} records`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    return false;
  }
}

async function addCriticalMedicines() {
  console.log('\n🏥 Adding critical medicines for OCR testing...');
  
  const criticalMedicines = [
    {
      generic_name: 'paracetamol',
      brand_names: ['Panadol', 'Panadol Extra', 'Efferalgan', 'Doliprane'],
      alternative_names: ['acetaminophen', 'APAP'],
      medicine_type: 'analgesic',
      therapeutic_class: 'Non-opioid analgesic',
      strength: '500mg',
      dosage_forms: ['tablet', 'capsule', 'liquid'],
      route_of_administration: ['oral'],
      indications: ['pain relief', 'fever reduction', 'headache'],
      side_effects: ['nausea', 'rash', 'liver damage with overdose'],
      warnings: ['Do not exceed 4g per day', 'Avoid alcohol'],
      typical_dosage: '500-1000mg every 4-6 hours',
      prescription_required: false,
      over_the_counter: true,
      popularity_score: 100
    },
    {
      generic_name: 'caffeine',
      brand_names: ['Caffeine', 'No-Doz'],
      alternative_names: ['methyltheobromine'],
      medicine_type: 'stimulant',
      therapeutic_class: 'CNS stimulant',
      strength: '200mg',
      dosage_forms: ['tablet', 'capsule'],
      route_of_administration: ['oral'],
      indications: ['fatigue', 'drowsiness', 'headache adjuvant'],
      side_effects: ['jitteriness', 'insomnia', 'rapid heartbeat'],
      warnings: ['Limit daily intake', 'May cause dependence'],
      typical_dosage: '100-200mg as needed',
      prescription_required: false,
      over_the_counter: true,
      popularity_score: 80
    },
    {
      generic_name: 'pantoprazole',
      brand_names: ['Protonix', 'Pantoloc'],
      alternative_names: ['PPI'],
      medicine_type: 'gastrointestinal',
      therapeutic_class: 'Proton pump inhibitor',
      strength: '40mg',
      dosage_forms: ['tablet', 'injection'],
      route_of_administration: ['oral', 'intravenous'],
      indications: ['GERD', 'peptic ulcer', 'acid reflux'],
      side_effects: ['headache', 'diarrhea', 'nausea'],
      warnings: ['Long-term use may affect bone density'],
      typical_dosage: '40mg once daily',
      prescription_required: true,
      over_the_counter: false,
      popularity_score: 70
    }
  ];
  
  try {
    for (const medicine of criticalMedicines) {
      // Check if medicine already exists
      const { data: existing } = await supabase
        .from('medicines')
        .select('id')
        .eq('generic_name', medicine.generic_name)
        .single();
      
      if (existing) {
        console.log(`⏭️ ${medicine.generic_name} already exists, skipping...`);
        continue;
      }
      
      // Insert new medicine
      const { data, error } = await supabase
        .from('medicines')
        .insert([medicine])
        .select('id, generic_name')
        .single();
      
      if (error) {
        console.error(`❌ Failed to add ${medicine.generic_name}:`, error);
      } else {
        console.log(`✅ Added ${medicine.generic_name} (ID: ${data.id})`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Failed to add critical medicines:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting database schema test...');
  
  const schemaOk = await testDatabaseSchema();
  
  if (schemaOk) {
    console.log('\n✅ Database schema is working correctly!');
    
    // Add critical medicines for testing
    await addCriticalMedicines();
    
    console.log('\n🎯 Database is ready for OCR testing!');
    console.log('📋 Next steps:');
    console.log('   1. Test OCR with "Panadol Extra" image');
    console.log('   2. Verify exact match detection');
    console.log('   3. Test confidence gating');
    
  } else {
    console.log('\n❌ Database schema has issues. Please check:');
    console.log('   1. Supabase connection');
    console.log('   2. Migration files applied');
    console.log('   3. RLS policies configured');
  }
}

// Run the test
main().catch(console.error);
