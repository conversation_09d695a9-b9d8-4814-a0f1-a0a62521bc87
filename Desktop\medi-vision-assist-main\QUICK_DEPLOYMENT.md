# Quick Deployment - Fix Chatbot 500 Errors

## 🚨 Critical Issues Fixed
1. **Infinite recursion in RLS policies** (causing profiles 500 errors)
2. **Poor error handling in Edge Function** (causing medical-chat 500 errors)

## ⚡ Quick Deploy (5 minutes)

### 1. Fix Database (RLS Infinite Recursion)
**Go to Supabase Dashboard > SQL Editor** and run this:

```sql
-- Fix infinite recursion in RLS policies
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all scans" ON public.medicine_scans;
DROP POLICY IF EXISTS "Ad<PERSON> can view all chat sessions" ON public.chat_sessions;
DROP POLICY IF EXISTS "Ad<PERSON> can view all feedback" ON public.chat_feedback;
DROP POLICY IF EXISTS "Ad<PERSON> can update feedback" ON public.chat_feedback;
DROP POLICY IF EXISTS "Ad<PERSON> can view all logs" ON public.system_logs;

-- Create function to check admin status (avoids recursion)
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION public.is_admin TO authenticated;

-- Recreate admin policies using the function (no recursion)
CREATE POLICY "Admins can view all profiles" ON public.profiles
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can view all scans" ON public.medicine_scans
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can view all chat sessions" ON public.chat_sessions
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can view all feedback" ON public.chat_feedback
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can update feedback" ON public.chat_feedback
  FOR UPDATE USING (public.is_admin());

CREATE POLICY "Admins can view all logs" ON public.system_logs
  FOR SELECT USING (public.is_admin());

CREATE POLICY "Service role can insert profiles" ON public.profiles
  FOR INSERT WITH CHECK (true);
```

### 2. Set Environment Variables
**Go to Supabase Dashboard > Settings > Edge Functions** and add:

| Variable | Value |
|----------|-------|
| `OPENROUTER_API_KEY` | `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` |
| `NODE_ENV` | `development` |

### 3. Deploy Edge Function
The improved `medical-chat` function is already in your codebase. If using Supabase CLI:
```bash
supabase functions deploy medical-chat
```

If not using CLI, the function will auto-deploy from your connected repository.

## ✅ Test the Fixes

### Quick Test 1: Medical Chat
Open browser console and run:
```javascript
fetch('https://ygkxdctaraeragizxfbt.supabase.co/functions/v1/medical-chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'
  },
  body: JSON.stringify({
    question: "hello",
    medicineName: "General Medical Assistant",
    userId: "fc37eb65-c52a-4e9e-a718-f2d2b4d8d324"
  })
}).then(r => r.json()).then(console.log)
```

**Expected:** `{success: true, reply: "...", responseTime: 1234}`

### Quick Test 2: Profiles Query
```javascript
fetch('https://ygkxdctaraeragizxfbt.supabase.co/rest/v1/profiles?select=is_admin&id=eq.fc37eb65-c52a-4e9e-a718-f2d2b4d8d324', {
  headers: {
    'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'
  }
}).then(r => r.json()).then(console.log)
```

**Expected:** `[]` or `[{is_admin: false}]` (no 500 error)

## 🎯 What Should Work Now

✅ **Medical Chat:** Users can send messages and get AI responses  
✅ **Profiles Query:** No more infinite recursion errors  
✅ **Admin Panel:** Should load without 500 errors  
✅ **Error Messages:** Specific, helpful error messages instead of generic ones  
✅ **Debugging:** Detailed logs in Supabase Dashboard > Edge Functions > Logs  

## 🔍 If Still Having Issues

1. **Check Supabase Function Logs:**
   - Dashboard > Edge Functions > medical-chat > Logs
   - Look for step-by-step execution logs

2. **Verify Environment Variables:**
   - Dashboard > Settings > Edge Functions
   - Ensure `OPENROUTER_API_KEY` is set

3. **Test Individual Components:**
   - Run `node test-backend-fixes.js`
   - Or open `test-fixes.html` in browser

## 📞 Emergency Rollback

If something breaks:
```sql
-- Disable RLS temporarily to restore access
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
```

Then investigate the specific issue using the detailed logs.
