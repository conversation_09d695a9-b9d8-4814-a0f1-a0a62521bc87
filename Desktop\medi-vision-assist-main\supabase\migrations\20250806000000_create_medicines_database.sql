-- Create comprehensive medicines database
-- This migration creates a robust medicine database with fuzzy search capabilities

-- Create medicines table
CREATE TABLE IF NOT EXISTS public.medicines (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Basic medicine information
    generic_name TEXT NOT NULL,
    brand_names TEXT[] DEFAULT '{}',
    alternative_names TEXT[] DEFAULT '{}',
    
    -- Medicine classification
    medicine_type TEXT NOT NULL DEFAULT 'unknown',
    therapeutic_class TEXT,
    pharmacological_class TEXT,
    controlled_substance_schedule TEXT,
    
    -- Dosage and formulation
    strength TEXT,
    dosage_forms TEXT[] DEFAULT '{}', -- tablet, capsule, liquid, etc.
    route_of_administration TEXT[] DEFAULT '{}', -- oral, topical, injection, etc.
    
    -- Medical information
    indications TEXT[], -- what it treats
    contraindications TEXT[], -- when not to use
    side_effects TEXT[], -- possible side effects
    warnings TEXT[], -- important warnings
    interactions TEXT[], -- drug interactions
    
    -- Usage information
    typical_dosage TEXT,
    frequency TEXT,
    duration TEXT,
    special_instructions TEXT,
    
    -- Regulatory information
    fda_approved BOOLEAN DEFAULT false,
    prescription_required BOOLEAN DEFAULT true,
    over_the_counter BOOLEAN DEFAULT false,
    
    -- Search optimization
    search_vector tsvector,
    popularity_score INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    CONSTRAINT medicines_generic_name_check CHECK (length(generic_name) >= 2),
    CONSTRAINT medicines_type_check CHECK (medicine_type IN (
        'analgesic', 'antibiotic', 'antiviral', 'antifungal', 'antihistamine',
        'antidepressant', 'antianxiety', 'antihypertensive', 'diabetes',
        'cardiovascular', 'respiratory', 'gastrointestinal', 'dermatological',
        'neurological', 'hormonal', 'vitamin', 'supplement', 'unknown'
    ))
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_medicines_generic_name ON public.medicines(generic_name);
CREATE INDEX IF NOT EXISTS idx_medicines_type ON public.medicines(medicine_type);
CREATE INDEX IF NOT EXISTS idx_medicines_search_vector ON public.medicines USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_medicines_brand_names ON public.medicines USING GIN(brand_names);
CREATE INDEX IF NOT EXISTS idx_medicines_alternative_names ON public.medicines USING GIN(alternative_names);
CREATE INDEX IF NOT EXISTS idx_medicines_popularity ON public.medicines(popularity_score DESC);

-- Create function to update search vector
CREATE OR REPLACE FUNCTION update_medicine_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('english', COALESCE(NEW.generic_name, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(NEW.brand_names, ' '), '')), 'B') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(NEW.alternative_names, ' '), '')), 'C') ||
        setweight(to_tsvector('english', COALESCE(NEW.medicine_type, '')), 'D') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(NEW.indications, ' '), '')), 'D');
    
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update search vector
DROP TRIGGER IF EXISTS trigger_update_medicine_search_vector ON public.medicines;
CREATE TRIGGER trigger_update_medicine_search_vector
    BEFORE INSERT OR UPDATE ON public.medicines
    FOR EACH ROW
    EXECUTE FUNCTION update_medicine_search_vector();

-- Create function for fuzzy medicine search
CREATE OR REPLACE FUNCTION search_medicines(
    search_term TEXT,
    limit_count INTEGER DEFAULT 10,
    similarity_threshold REAL DEFAULT 0.3
)
RETURNS TABLE (
    id UUID,
    generic_name TEXT,
    brand_names TEXT[],
    medicine_type TEXT,
    strength TEXT,
    indications TEXT[],
    similarity_score REAL,
    search_rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.generic_name,
        m.brand_names,
        m.medicine_type,
        m.strength,
        m.indications,
        GREATEST(
            similarity(m.generic_name, search_term),
            COALESCE((
                SELECT MAX(similarity(brand_name, search_term))
                FROM unnest(m.brand_names) AS brand_name
            ), 0),
            COALESCE((
                SELECT MAX(similarity(alt_name, search_term))
                FROM unnest(m.alternative_names) AS alt_name
            ), 0)
        ) as similarity_score,
        ts_rank(m.search_vector, plainto_tsquery('english', search_term)) as search_rank
    FROM public.medicines m
    WHERE 
        -- Text similarity search
        (
            similarity(m.generic_name, search_term) > similarity_threshold OR
            EXISTS (
                SELECT 1 FROM unnest(m.brand_names) AS brand_name
                WHERE similarity(brand_name, search_term) > similarity_threshold
            ) OR
            EXISTS (
                SELECT 1 FROM unnest(m.alternative_names) AS alt_name
                WHERE similarity(alt_name, search_term) > similarity_threshold
            )
        )
        OR
        -- Full-text search
        m.search_vector @@ plainto_tsquery('english', search_term)
    ORDER BY 
        similarity_score DESC,
        search_rank DESC,
        m.popularity_score DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Enable pg_trgm extension for fuzzy string matching
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Update medicine_scans table to reference medicines table
ALTER TABLE public.medicine_scans 
ADD COLUMN IF NOT EXISTS medicine_id UUID REFERENCES public.medicines(id),
ADD COLUMN IF NOT EXISTS confidence_level TEXT DEFAULT 'low' CHECK (confidence_level IN ('low', 'medium', 'high')),
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'unverified' CHECK (verification_status IN ('unverified', 'verified', 'rejected'));

-- Create index for medicine_scans foreign key
CREATE INDEX IF NOT EXISTS idx_medicine_scans_medicine_id ON public.medicine_scans(medicine_id);

-- Insert sample medicines data
INSERT INTO public.medicines (
    generic_name, brand_names, alternative_names, medicine_type, 
    therapeutic_class, strength, dosage_forms, route_of_administration,
    indications, side_effects, warnings, typical_dosage, frequency,
    prescription_required, over_the_counter, popularity_score
) VALUES 
-- Pain relievers
('acetaminophen', ARRAY['Tylenol', 'Panadol', 'Paracetamol'], ARRAY['APAP'], 'analgesic', 
 'Non-opioid analgesic', '500mg', ARRAY['tablet', 'capsule', 'liquid'], ARRAY['oral'],
 ARRAY['pain relief', 'fever reduction'], ARRAY['nausea', 'rash'], 
 ARRAY['liver damage with overdose'], '500-1000mg', 'every 4-6 hours', false, true, 95),

('ibuprofen', ARRAY['Advil', 'Motrin', 'Nurofen'], ARRAY[], 'analgesic',
 'NSAID', '200mg', ARRAY['tablet', 'capsule', 'liquid'], ARRAY['oral'],
 ARRAY['pain relief', 'inflammation', 'fever'], ARRAY['stomach upset', 'dizziness'],
 ARRAY['GI bleeding risk'], '200-400mg', 'every 4-6 hours', false, true, 90),

('aspirin', ARRAY['Bayer', 'Bufferin'], ARRAY['ASA'], 'analgesic',
 'NSAID', '325mg', ARRAY['tablet', 'chewable'], ARRAY['oral'],
 ARRAY['pain relief', 'heart attack prevention'], ARRAY['stomach irritation'],
 ARRAY['bleeding risk', 'Reye syndrome in children'], '325-650mg', 'every 4 hours', false, true, 85),

-- Antibiotics
('amoxicillin', ARRAY['Amoxil', 'Trimox'], ARRAY[], 'antibiotic',
 'Penicillin', '500mg', ARRAY['capsule', 'tablet', 'liquid'], ARRAY['oral'],
 ARRAY['bacterial infections'], ARRAY['diarrhea', 'nausea', 'rash'],
 ARRAY['allergic reactions'], '500mg', 'every 8 hours', true, false, 80),

('azithromycin', ARRAY['Zithromax', 'Z-Pak'], ARRAY[], 'antibiotic',
 'Macrolide', '250mg', ARRAY['tablet', 'capsule', 'liquid'], ARRAY['oral'],
 ARRAY['respiratory infections', 'skin infections'], ARRAY['nausea', 'diarrhea'],
 ARRAY['heart rhythm changes'], '500mg day 1, then 250mg', 'daily for 4 days', true, false, 75),

-- Cardiovascular
('lisinopril', ARRAY['Prinivil', 'Zestril'], ARRAY[], 'antihypertensive',
 'ACE inhibitor', '10mg', ARRAY['tablet'], ARRAY['oral'],
 ARRAY['high blood pressure', 'heart failure'], ARRAY['dry cough', 'dizziness'],
 ARRAY['kidney problems', 'high potassium'], '10-20mg', 'daily', true, false, 70),

('atorvastatin', ARRAY['Lipitor'], ARRAY[], 'cardiovascular',
 'Statin', '20mg', ARRAY['tablet'], ARRAY['oral'],
 ARRAY['high cholesterol'], ARRAY['muscle pain', 'liver problems'],
 ARRAY['muscle breakdown', 'liver damage'], '20-40mg', 'daily', true, false, 65),

-- Diabetes
('metformin', ARRAY['Glucophage'], ARRAY[], 'diabetes',
 'Biguanide', '500mg', ARRAY['tablet', 'extended-release'], ARRAY['oral'],
 ARRAY['type 2 diabetes'], ARRAY['nausea', 'diarrhea'],
 ARRAY['lactic acidosis'], '500-1000mg', 'twice daily', true, false, 60);

-- Set up Row Level Security
ALTER TABLE public.medicines ENABLE ROW LEVEL SECURITY;

-- Policy for reading medicines (public access)
CREATE POLICY "Anyone can read medicines" ON public.medicines
    FOR SELECT USING (true);

-- Policy for inserting medicines (authenticated users only)
CREATE POLICY "Authenticated users can insert medicines" ON public.medicines
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy for updating medicines (only creators or admins)
CREATE POLICY "Users can update their medicines" ON public.medicines
    FOR UPDATE USING (
        auth.uid() = created_by OR 
        auth.jwt() ->> 'role' = 'admin'
    );

-- Create function to increment popularity score
CREATE OR REPLACE FUNCTION increment_popularity(medicine_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.medicines
    SET popularity_score = popularity_score + 1,
        updated_at = NOW()
    WHERE id = medicine_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT ON public.medicines TO anon, authenticated;
GRANT INSERT, UPDATE ON public.medicines TO authenticated;
GRANT EXECUTE ON FUNCTION search_medicines TO anon, authenticated;
GRANT EXECUTE ON FUNCTION increment_popularity TO authenticated;
