/**
 * Comprehensive test for the fallback system
 */

const SUPABASE_URL = 'https://ygkxdctaraeragizxfbt.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'

async function testQuestion(question, description) {
    console.log(`\n🧪 Testing: ${description}`)
    console.log(`❓ Question: "${question}"`)
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
                question: question,
                userId: 'test-user'
            })
        })

        console.log(`📡 Status: ${response.status}`)
        const data = await response.json()
        
        if (data.success) {
            console.log(`✅ Success: ${data.reply.substring(0, 100)}...`)
            console.log(`📍 Source: ${data.source}`)
            if (data.note) console.log(`📝 Note: ${data.note}`)
            console.log(`⏱️ Response time: ${data.responseTime}ms`)
        } else {
            console.log(`❌ Error: ${data.error}`)
        }
        
    } catch (error) {
        console.log(`❌ Network error: ${error.message}`)
    }
}

async function runComprehensiveTest() {
    console.log('🚀 Starting comprehensive fallback system test...')
    
    // Test different types of questions
    await testQuestion('hi', 'Greeting')
    await testQuestion('hello', 'Another greeting')
    await testQuestion('What is aspirin?', 'Medicine question')
    await testQuestion('What is ibuprofen used for?', 'Pain relief question')
    await testQuestion('Tell me about diabetes', 'Health condition question')
    await testQuestion('What vitamins should I take?', 'Vitamin question')
    await testQuestion('I have a headache', 'Symptom question')
    await testQuestion('What is MediVision?', 'About the chatbot')
    await testQuestion('...', 'Unclear input')
    await testQuestion('xyz123', 'Random input')
    
    console.log('\n🎉 Comprehensive test completed!')
}

runComprehensiveTest()
