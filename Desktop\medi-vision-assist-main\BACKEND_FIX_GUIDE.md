# Backend Fix Guide - Supa<PERSON> Chatbot Issues

## 🔍 Issues Identified and Fixed

### 1. **Infinite Recursion in RLS Policies** ✅ FIXED
**Problem:** The admin policy on `profiles` table was querying the same table, causing infinite recursion.

**Root Cause:**
```sql
-- PROBLEMATIC POLICY (caused infinite recursion)
CREATE POLICY "<PERSON><PERSON> can view all profiles" ON public.profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles  -- ❌ Querying profiles from profiles policy
      WHERE id = auth.uid() AND is_admin = true
    )
  );
```

**Solution:** Created a `SECURITY DEFINER` function to break the recursion:
```sql
-- ✅ FIXED: Function bypasses RLS
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ✅ FIXED: Policy uses function (no recursion)
CREATE POLICY "<PERSON><PERSON> can view all profiles" ON public.profiles
  FOR SELECT USING (public.is_admin());
```

### 2. **Edge Function Error Handling** ✅ IMPROVED
**Problem:** Poor error handling and logging made debugging impossible.

**Improvements:**
- ✅ Step-by-step logging with clear indicators
- ✅ Specific error messages for different failure types
- ✅ Environment variable validation with detailed feedback
- ✅ Network error handling for OpenRouter API calls
- ✅ JSON parsing error handling
- ✅ Database operation error handling (non-blocking)

## 🚀 Deployment Steps

### Step 1: Apply Database Migration
```bash
# If using Supabase CLI:
supabase db push

# Or apply the migration manually in Supabase Dashboard > SQL Editor:
# Copy and run the content of: 20250703000000_fix_rls_infinite_recursion.sql
```

### Step 2: Set Environment Variables
Go to **Supabase Dashboard > Settings > Edge Functions** and add:

```env
OPENROUTER_API_KEY=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
NODE_ENV=development
```

### Step 3: Deploy Edge Function
```bash
# If using Supabase CLI:
supabase functions deploy medical-chat

# The function will be automatically deployed if you're using the dashboard
```

### Step 4: Test the Fixes
```bash
# Run the test script:
node test-backend-fixes.js

# Or open test-fixes.html in browser and run tests
```

## 🧪 Testing Guide

### Test 1: Medical Chat Function
```javascript
// Expected: 200 OK with success: true
fetch('https://ygkxdctaraeragizxfbt.supabase.co/functions/v1/medical-chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_ANON_KEY'
  },
  body: JSON.stringify({
    question: "hello",
    medicineName: "General Medical Assistant",
    userId: "test-user-id"
  })
})
```

### Test 2: Profiles Query
```javascript
// Expected: 200 OK with profile data or empty array
fetch('https://ygkxdctaraeragizxfbt.supabase.co/rest/v1/profiles?select=is_admin&id=eq.USER_ID', {
  headers: {
    'apikey': 'YOUR_ANON_KEY',
    'Authorization': 'Bearer YOUR_ANON_KEY'
  }
})
```

## 📊 Monitoring and Logs

### Supabase Function Logs
1. Go to **Supabase Dashboard > Edge Functions > medical-chat > Logs**
2. Look for these log patterns:

**Success Indicators:**
```
🚀 Medical chat function called - Method: POST
✅ OpenRouter API key is set
✅ Supabase environment variables are set
📥 Request payload: {...}
🤖 Processing medical chat request for user: ...
📡 OpenRouter API response status: 200
✅ Chat response generated in XXXms
✅ Chat session stored with ID: ...
```

**Error Indicators:**
```
❌ OPENROUTER_API_KEY environment variable is not set
❌ Failed to parse request body: ...
❌ Network error calling OpenRouter API: ...
❌ OpenRouter API error: {...}
❌ Failed to store chat session: {...}
```

### Browser Console Logs
Enable detailed logging in your app:
```javascript
// In browser console:
localStorage.setItem('debug', 'true');
```

## 🔧 Troubleshooting

### Issue: Still getting 500 errors from medical-chat
**Check:**
1. Environment variables are set in Supabase Dashboard
2. OpenRouter API key is valid and has credits
3. Function is deployed with latest code
4. Check function logs for specific error messages

### Issue: Still getting infinite recursion on profiles
**Check:**
1. Migration was applied successfully
2. Old policies were dropped
3. New `is_admin()` function exists
4. New policies are using the function

### Issue: Profile not found errors
**Check:**
1. User profile was created by the trigger
2. RLS policies allow user to read their own profile
3. User is properly authenticated

## 📋 Verification Checklist

- [ ] Migration applied successfully
- [ ] Environment variables set in Supabase
- [ ] Edge function deployed
- [ ] Medical chat returns 200 OK
- [ ] Profiles query returns 200 OK (no infinite recursion)
- [ ] Function logs show detailed step-by-step execution
- [ ] Error messages are specific and helpful
- [ ] Chat responses are generated successfully

## 🔄 Rollback Plan

If issues persist:

1. **Revert Migration:**
```sql
-- Drop the new function and policies
DROP FUNCTION IF EXISTS public.is_admin;
-- Recreate original policies (without the problematic admin ones)
```

2. **Revert Function:**
```bash
git checkout HEAD~1 -- supabase/functions/medical-chat/index.ts
supabase functions deploy medical-chat
```

## 📞 Support

If you continue to experience issues:

1. Check the function logs in Supabase Dashboard
2. Run the test script to isolate the problem
3. Verify all environment variables are set correctly
4. Ensure your OpenRouter API key is valid and has credits

The fixes address the root causes of both the infinite recursion and the Edge Function errors. The improved logging will help identify any remaining issues quickly.
