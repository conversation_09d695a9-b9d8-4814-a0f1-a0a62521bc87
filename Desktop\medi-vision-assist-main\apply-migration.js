/**
 * Apply the medicines database migration directly
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';

// Initialize Supabase client with service role key (needed for DDL operations)
const supabaseUrl = 'https://ygkxdctaraeragizxfbt.supabase.co';
// Note: We need the service role key for creating tables, but we don't have it
// Let's try with the anon key first and see what permissions we have
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlna3hkY3RhcmFlcmFnaXp4ZmJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNDU3MTksImV4cCI6MjA2NTgyMTcxOX0.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration() {
  console.log('🚀 Applying medicines database migration...');
  
  try {
    // Read the migration file
    const migrationSQL = readFileSync('./supabase/migrations/20250806000000_create_medicines_database.sql', 'utf8');
    
    console.log('📄 Migration file loaded, length:', migrationSQL.length, 'characters');
    
    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log('📝 Found', statements.length, 'SQL statements to execute');
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.length < 10) continue; // Skip very short statements
      
      console.log(`\n${i + 1}/${statements.length} Executing:`, statement.substring(0, 100) + '...');
      
      try {
        const { data, error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error('❌ Error executing statement:', error);
          
          // Try alternative method for some statements
          if (statement.includes('CREATE TABLE') || statement.includes('CREATE EXTENSION')) {
            console.log('🔄 Trying alternative execution method...');
            // For now, we'll skip DDL statements that require elevated privileges
            console.log('⏭️ Skipping DDL statement (requires service role)');
            continue;
          }
        } else {
          console.log('✅ Statement executed successfully');
        }
      } catch (err) {
        console.error('❌ Exception executing statement:', err);
      }
    }
    
    console.log('\n🎯 Migration application completed!');
    
    // Test if the migration worked
    await testMigrationResult();
    
  } catch (error) {
    console.error('❌ Failed to apply migration:', error);
  }
}

async function testMigrationResult() {
  console.log('\n🧪 Testing migration result...');
  
  try {
    // Test if medicines table exists
    const { data, error } = await supabase
      .from('medicines')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Medicines table still not accessible:', error);
      console.log('\n💡 The migration requires service role privileges.');
      console.log('📋 Next steps:');
      console.log('   1. Go to Supabase Dashboard > SQL Editor');
      console.log('   2. Copy and paste the migration file content');
      console.log('   3. Execute it manually');
      console.log('   4. Or use Supabase CLI: supabase db push');
    } else {
      console.log('✅ Medicines table is now accessible!');
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Alternative: Create a simplified version that works with anon key
async function createSimplifiedSchema() {
  console.log('\n🔧 Creating simplified schema with available permissions...');
  
  // We can't create tables with anon key, but we can test what we can do
  try {
    // Test what functions are available
    const { data, error } = await supabase.rpc('version');
    
    if (error) {
      console.log('❌ Cannot execute RPC functions:', error);
    } else {
      console.log('✅ RPC functions are available');
    }
    
    // Check if we can access any existing tables
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');
    
    if (tablesError) {
      console.log('❌ Cannot access table information:', tablesError);
    } else {
      console.log('📋 Available tables:', tables?.map(t => t.table_name) || []);
    }
    
  } catch (error) {
    console.error('❌ Schema exploration failed:', error);
  }
}

async function main() {
  console.log('🏥 MediVision Database Migration Tool');
  console.log('=====================================');
  
  await applyMigration();
  await createSimplifiedSchema();
  
  console.log('\n📋 Manual Migration Instructions:');
  console.log('1. Go to https://supabase.com/dashboard/project/ygkxdctaraeragizxfbt/sql');
  console.log('2. Copy the content of supabase/migrations/20250806000000_create_medicines_database.sql');
  console.log('3. Paste it in the SQL Editor and click "Run"');
  console.log('4. Then run: node test-database-schema.js');
}

main().catch(console.error);
