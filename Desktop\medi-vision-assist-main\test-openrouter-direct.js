// Direct test of OpenRouter API to debug the 404 error
const API_KEY = "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";

async function testOpenRouterDirect() {
  console.log('🧪 Testing OpenRouter API directly...');
  console.log('🔑 API Key length:', API_KEY.length, 'chars');
  console.log('🔑 API Key starts with:', API_KEY.substring(0, 20) + '...');
  
  try {
    console.log('📡 Making request to OpenRouter API...');
    
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://ygkxdctaraeragizxfbt.supabase.co',
        'X-Title': 'MediVision Assist'
      },
      body: JSON.stringify({
        model: 'meta-llama/llama-3.2-3b-instruct:free',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful medical assistant.'
          },
          {
            role: 'user',
            content: 'Hello, test message'
          }
        ],
        max_tokens: 100,
        temperature: 0.3
      })
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('📥 Raw response:', responseText);
    
    if (response.ok) {
      const data = JSON.parse(responseText);
      console.log('✅ OpenRouter API call successful!');
      console.log('💬 Response:', data.choices[0].message.content);
    } else {
      console.log('❌ OpenRouter API call failed');
      console.log('Error details:', responseText);
      
      // Try to parse error response
      try {
        const errorData = JSON.parse(responseText);
        console.log('📋 Parsed error:', errorData);
      } catch (e) {
        console.log('📋 Could not parse error response as JSON');
      }
    }
    
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
}

// Test different models
async function testDifferentModels() {
  const models = [
    'meta-llama/llama-3.2-3b-instruct:free',
    'microsoft/phi-3-mini-128k-instruct:free',
    'google/gemma-2-9b-it:free',
    'qwen/qwen-2-7b-instruct:free'
  ];
  
  for (const model of models) {
    console.log(`\n🧪 Testing model: ${model}`);
    
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://ygkxdctaraeragizxfbt.supabase.co',
          'X-Title': 'MediVision Assist'
        },
        body: JSON.stringify({
          model: model,
          messages: [{ role: 'user', content: 'Hi' }],
          max_tokens: 50
        })
      });
      
      console.log(`📡 ${model}: Status ${response.status}`);
      
      if (response.ok) {
        console.log(`✅ ${model}: WORKS!`);
        break; // Found a working model
      } else {
        const errorText = await response.text();
        console.log(`❌ ${model}: ${errorText.substring(0, 100)}...`);
      }
      
    } catch (error) {
      console.log(`❌ ${model}: Network error - ${error.message}`);
    }
  }
}

// Run tests
async function runTests() {
  await testOpenRouterDirect();
  console.log('\n' + '='.repeat(60) + '\n');
  await testDifferentModels();
}

runTests();
