/**
 * Test script to verify the fallback system identity responses
 * This tests the local knowledge base when OpenRouter API is not available
 */

const SUPABASE_URL = 'https://ygkxdctaraeragizxfbt.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'

async function testFallbackIdentity(question, testName) {
    console.log(`\n🧪 Testing Fallback: ${testName}`)
    console.log(`📝 Question: "${question}"`)
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
                question: question,
                userId: 'test-fallback-user',
                // Force fallback by using a parameter that might trigger it
                forceTest: true
            })
        })

        const data = await response.json()
        
        if (data.success) {
            console.log(`✅ Response: ${data.reply}`)
            console.log(`📍 Source: ${data.source || 'openrouter'}`)
            
            // Check if the response contains the correct identity
            const correctIdentity = data.reply.includes('Med Amine Chouchane')
            const incorrectIdentity = data.reply.includes('Medine Chane') || data.reply.includes('Google DeepMind') || data.reply.includes('Gemma')
            
            if (correctIdentity && !incorrectIdentity) {
                console.log(`🎯 IDENTITY CHECK: ✅ CORRECT`)
                return true
            } else if (incorrectIdentity) {
                console.log(`🎯 IDENTITY CHECK: ❌ INCORRECT - Contains wrong identity`)
                return false
            } else {
                console.log(`🎯 IDENTITY CHECK: ⚠️ MISSING - No identity mentioned`)
                return false
            }
        } else {
            console.log(`❌ Error: ${data.error}`)
            if (data.reply) {
                console.log(`💬 Fallback reply: ${data.reply}`)
            }
            return false
        }
    } catch (error) {
        console.log(`❌ Network error: ${error.message}`)
        return false
    }
}

async function runFallbackTests() {
    console.log('🚀 Starting Fallback Identity Tests')
    console.log('=' .repeat(60))
    
    // Test identity questions that should work with fallback
    const identityQuestions = [
        { question: "Who created you?", name: "Creator Question" },
        { question: "Who made you?", name: "Made Question" },
        { question: "Who developed you?", name: "Developer Question" },
        { question: "Who are you?", name: "Identity Question" },
        { question: "What is your name?", name: "Name Question" }
    ]
    
    let correctResponses = 0
    let totalTests = identityQuestions.length
    
    for (const test of identityQuestions) {
        const result = await testFallbackIdentity(test.question, test.name)
        if (result) {
            correctResponses++
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    console.log('\n' + '=' .repeat(60))
    console.log('🏁 Fallback Identity Test Results')
    console.log(`✅ Correct responses: ${correctResponses}/${totalTests}`)
    console.log(`📊 Success rate: ${Math.round((correctResponses/totalTests) * 100)}%`)
    
    if (correctResponses === totalTests) {
        console.log('🎉 ALL FALLBACK TESTS PASSED!')
    } else {
        console.log('⚠️ Some fallback tests failed - check knowledge base')
    }
    
    // Test a few medical questions to ensure fallback is working
    console.log('\n📋 Testing medical fallback responses...')
    await testFallbackIdentity('What is aspirin?', 'Medical: Aspirin')
    await testFallbackIdentity('hello', 'Greeting Test')
}

// Run the tests
runFallbackTests().catch(console.error)
