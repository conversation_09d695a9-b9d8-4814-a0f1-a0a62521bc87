// Test if environment variables are working in the Edge Function
const SUPABASE_URL = "https://ygkxdctaraeragizxfbt.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc";

async function testEnvironmentVariables() {
  console.log('🧪 Testing if environment variables are loaded...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({
        question: "test env vars",
        medicineName: "Test",
        userId: "test-user"
      })
    });

    console.log(`📡 Response status: ${response.status}`);
    
    const data = await response.json();
    console.log('📥 Response data:', JSON.stringify(data, null, 2));
    
    // Check if the error mentions environment variables
    if (data.error && data.error.includes('OpenRouter API key is not configured')) {
      console.log('❌ Environment variables are NOT being read by the function');
      console.log('💡 Solution: The function needs to be redeployed to pick up new environment variables');
    } else if (data.error && data.error.includes('OpenRouter API error: 404')) {
      console.log('✅ Environment variables are being read');
      console.log('❌ But the API call is failing - might be wrong model or API endpoint');
    } else if (data.success) {
      console.log('✅ Everything is working perfectly!');
    }
    
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
}

// Run the test
testEnvironmentVariables();
