-- Add OCR-specific fields to medicine_scans table
ALTER TABLE public.medicine_scans 
ADD COLUMN IF NOT EXISTS processing_time INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS image_name TEXT;

-- Add index for better query performance
CREATE INDEX IF NOT EXISTS idx_medicine_scans_user_id ON public.medicine_scans(user_id);
CREATE INDEX IF NOT EXISTS idx_medicine_scans_created_at ON public.medicine_scans(created_at);
CREATE INDEX IF NOT EXISTS idx_medicine_scans_scan_status ON public.medicine_scans(scan_status);

-- Update scan_status to use enum-like constraint
ALTER TABLE public.medicine_scans 
DROP CONSTRAINT IF EXISTS medicine_scans_scan_status_check;

ALTER TABLE public.medicine_scans 
ADD CONSTRAINT medicine_scans_scan_status_check 
CHECK (scan_status IN ('processing', 'completed', 'failed'));

-- Add comment to table
COMMENT ON TABLE public.medicine_scans IS 'Stores OCR results and medicine identification data from image scans';
COMMENT ON COLUMN public.medicine_scans.processing_time IS 'OCR processing time in milliseconds';
COMMENT ON COLUMN public.medicine_scans.image_name IS 'Original filename of the uploaded image';
COMMENT ON COLUMN public.medicine_scans.confidence_score IS 'OCR confidence score (0-100)';
COMMENT ON COLUMN public.medicine_scans.extracted_text IS 'Raw text extracted from image via OCR';
