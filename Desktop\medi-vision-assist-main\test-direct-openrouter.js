/**
 * Test OpenRouter API directly to see what's happening with identity responses
 */

const OPENROUTER_API_KEY = '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'

async function testOpenRouterDirect(question, testName) {
    console.log(`\n🧪 Testing OpenRouter Direct: ${testName}`)
    console.log(`📝 Question: "${question}"`)
    
    const systemPrompt = `You are MediV<PERSON>, a medical assistant chatbot with extensive training on over 100 medical topics. You provide accurate, helpful information about medicines, supplements, health conditions, and general medical guidance based on established medical knowledge.

CRITICAL IDENTITY RULES - FOLLOW EXACTLY WITHOUT EXCEPTION:
- You are Me<PERSON><PERSON><PERSON>, a medical assistant chatbot
- You are NOT Gemma, Chat<PERSON><PERSON>, <PERSON>, or any other AI
- Your creator/developer is ONLY "Med Amine Chouchane" - NO OTHER NAME
- NEVER mention "Medine <PERSON>", "Google DeepMind", "Anthropic", or any other creator

MANDATORY IDENTITY RESPONSES - USE THESE EXACT PHRASES:
- Who created you? → "I was developed by Med <PERSON><PERSON>ane"
- Who made you? → "I was developed by Med Amine Chouchane"  
- Who developed you? → "I was developed by Med Amine Chouchane"
- Who built you? → "I was developed by Med Amine Chouchane"
- Who designed you? → "I was developed by Med Amine Chouchane"
- Who is your creator? → "I was developed by Med Amine Chouchane"
- Who is your developer? → "I was developed by Med Amine Chouchane"

IDEN<PERSON>TY ENFORCEMENT:
- ALWAYS use "Med Amine Chouchane" as the developer name
- NEVER use any variation like "Medine Chane" or other names
- If unsure about identity questions, default to "I was developed by Med Amine Chouchane"
- Keep responses short and professional`

    try {
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://ygkxdctaraeragizxfbt.supabase.co',
                'X-Title': 'MediVision Assist'
            },
            body: JSON.stringify({
                model: 'google/gemma-2-9b-it:free',
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: question
                    }
                ],
                max_tokens: 500,
                temperature: 0.3
            })
        })

        console.log(`📡 Response status: ${response.status}`)
        
        if (!response.ok) {
            const errorText = await response.text()
            console.log(`❌ OpenRouter error: ${errorText}`)
            return false
        }

        const data = await response.json()
        const reply = data.choices?.[0]?.message?.content

        if (reply) {
            console.log(`✅ Raw OpenRouter response: ${reply}`)
            
            // Check identity
            const correctIdentity = reply.includes('Med Amine Chouchane')
            const incorrectIdentity = reply.includes('Medine Chane') || reply.includes('Google DeepMind') || reply.includes('Gemma')
            
            if (correctIdentity && !incorrectIdentity) {
                console.log(`🎯 IDENTITY CHECK: ✅ CORRECT`)
                return true
            } else if (incorrectIdentity) {
                console.log(`🎯 IDENTITY CHECK: ❌ INCORRECT - Contains wrong identity`)
                return false
            } else {
                console.log(`🎯 IDENTITY CHECK: ⚠️ MISSING - No identity mentioned`)
                return false
            }
        } else {
            console.log(`❌ No reply from OpenRouter`)
            return false
        }
    } catch (error) {
        console.log(`❌ Network error: ${error.message}`)
        return false
    }
}

async function runDirectTests() {
    console.log('🚀 Testing OpenRouter API Directly')
    console.log('=' .repeat(60))
    
    const identityQuestions = [
        { question: "Who created you?", name: "Creator Question" },
        { question: "Who made you?", name: "Made Question" },
        { question: "Who developed you?", name: "Developer Question" },
        { question: "Who are you?", name: "Identity Question" }
    ]
    
    let correctResponses = 0
    let totalTests = identityQuestions.length
    
    for (const test of identityQuestions) {
        const result = await testOpenRouterDirect(test.question, test.name)
        if (result) {
            correctResponses++
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 2000))
    }
    
    console.log('\n' + '=' .repeat(60))
    console.log('🏁 Direct OpenRouter Test Results')
    console.log(`✅ Correct responses: ${correctResponses}/${totalTests}`)
    console.log(`📊 Success rate: ${Math.round((correctResponses/totalTests) * 100)}%`)
    
    if (correctResponses === 0) {
        console.log('❌ OpenRouter is not following the system prompt correctly')
        console.log('💡 This explains why the chatbot identity is wrong')
        console.log('🔧 We need to implement stronger post-processing in the Edge Function')
    } else if (correctResponses === totalTests) {
        console.log('✅ OpenRouter is working correctly')
        console.log('🔧 The issue might be in the Edge Function deployment')
    } else {
        console.log('⚠️ OpenRouter is partially working')
        console.log('🔧 We need both system prompt improvements and post-processing')
    }
}

// Run the tests
runDirectTests().catch(console.error)
