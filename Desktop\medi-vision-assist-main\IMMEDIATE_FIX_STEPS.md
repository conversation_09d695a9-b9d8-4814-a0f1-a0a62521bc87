# 🚨 IMMEDIATE FIX STEPS - Chatbot 500 Errors

## Current Status
✅ **Identified Issues:**
1. **Infinite recursion in RLS policies** (causing profiles 500 errors)
2. **OpenRouter API 404 error** (wrong model or missing API key)

❌ **Still Failing:**
- Medical chat function: `OpenRouter API error: 404`
- Profiles query: `infinite recursion detected in policy for relation "profiles"`

## 🔥 URGENT: Apply These Fixes NOW

### Step 1: Fix Database (RLS Infinite Recursion) - 2 minutes
**Go to Supabase Dashboard > SQL Editor** and copy-paste this entire SQL:

```sql
-- URGENT: Fix infinite recursion in RLS policies
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all scans" ON public.medicine_scans;
DROP POLICY IF EXISTS "Ad<PERSON> can view all chat sessions" ON public.chat_sessions;
DROP POLICY IF EXISTS "Ad<PERSON> can view all feedback" ON public.chat_feedback;
DROP POLICY IF EXISTS "Ad<PERSON> can update feedback" ON public.chat_feedback;
DROP POLICY IF EXISTS "Ad<PERSON> can view all logs" ON public.system_logs;

CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION public.is_admin TO authenticated;

CREATE POLICY "Admins can view all profiles" ON public.profiles
  FOR SELECT USING (public.is_admin());
CREATE POLICY "Admins can view all scans" ON public.medicine_scans
  FOR SELECT USING (public.is_admin());
CREATE POLICY "Admins can view all chat sessions" ON public.chat_sessions
  FOR SELECT USING (public.is_admin());
CREATE POLICY "Admins can view all feedback" ON public.chat_feedback
  FOR SELECT USING (public.is_admin());
CREATE POLICY "Admins can update feedback" ON public.chat_feedback
  FOR UPDATE USING (public.is_admin());
CREATE POLICY "Admins can view all logs" ON public.system_logs
  FOR SELECT USING (public.is_admin());
CREATE POLICY "Service role can insert profiles" ON public.profiles
  FOR INSERT WITH CHECK (true);

SELECT 'RLS fix applied successfully' as status;
```

**Click "Run" and verify you see:** `RLS fix applied successfully`

### Step 2: Set Environment Variables - 1 minute
**Go to Supabase Dashboard > Settings > Edge Functions** and add:

| Variable Name | Value |
|---------------|-------|
| `OPENROUTER_API_KEY` | `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` |
| `NODE_ENV` | `development` |

**Click "Save" after adding each variable.**

### Step 3: Deploy Edge Function - 1 minute
The function code is already updated. If you have Supabase CLI:
```bash
supabase functions deploy medical-chat
```

If no CLI, the function will auto-deploy from your repository.

## 🧪 Test the Fixes

### Test 1: Check RLS Fix
Open browser console and run:
```javascript
fetch('https://ygkxdctaraeragizxfbt.supabase.co/rest/v1/profiles?select=is_admin&id=eq.fc37eb65-c52a-4e9e-a718-f2d2b4d8d324', {
  headers: {
    'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'
  }
}).then(r => r.json()).then(console.log)
```

**Expected:** `[]` or `[{is_admin: false}]` (NOT a 500 error)

### Test 2: Check Medical Chat
```javascript
fetch('https://ygkxdctaraeragizxfbt.supabase.co/functions/v1/medical-chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'
  },
  body: JSON.stringify({
    question: "hello",
    medicineName: "General Medical Assistant",
    userId: "fc37eb65-c52a-4e9e-a718-f2d2b4d8d324"
  })
}).then(r => r.json()).then(console.log)
```

**Expected:** `{success: true, reply: "...", responseTime: 1234}`

## 🔍 If Still Having Issues

### Check Function Logs:
1. Go to **Supabase Dashboard > Edge Functions > medical-chat > Logs**
2. Look for these messages:
   - `✅ OpenRouter API key is set (length: XX chars)`
   - `📡 OpenRouter API response status: 200`
   - `✅ Chat response generated in XXXms`

### Common Issues:
- **Environment variables not saved:** Refresh the Edge Functions page and check they're still there
- **Function not deployed:** Wait 1-2 minutes for auto-deployment or use CLI
- **API key invalid:** Verify the OpenRouter API key is correct and has credits

## 🎯 Expected Results After Fixes

✅ **Profiles Query:** Returns `200 OK` (no infinite recursion)  
✅ **Medical Chat:** Returns `{success: true, reply: "..."}`  
✅ **Chatbot UI:** Users can send messages and get AI responses  
✅ **Admin Panel:** Loads without 500 errors  

## 📞 Emergency Contact

If you're still getting errors after these steps:
1. Check the exact error message in Supabase function logs
2. Verify environment variables are saved in Supabase Dashboard
3. Try the test commands above to isolate the issue

**The fixes are ready - they just need to be applied in Supabase Dashboard!**
