
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface MedicineLookupRequest {
  medicineName: string
  extractedText?: string
  userId?: string
}

interface RxNormResponse {
  drugGroup?: {
    conceptGroup?: Array<{
      conceptProperties?: Array<{
        name: string
        synonym?: string
        tty?: string
        rxcui: string
      }>
    }>
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { medicineName, extractedText, userId }: MedicineLookupRequest = await req.json()
    
    if (!medicineName) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Medicine name is required',
          suggestions: ['Please provide a medicine name to search']
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400 
        }
      )
    }
    
    console.log(`🔍 Looking up medicine: ${medicineName}`)
    
    // Try RxNorm API first
    const rxnormResult = await lookupRxNorm(medicineName)
    
    if (rxnormResult.success && rxnormResult.data) {
      console.log('✅ RxNorm lookup successful')
      return new Response(
        JSON.stringify({
          success: true,
          data: rxnormResult.data,
          source: 'rxnorm'
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    // Fallback to basic medicine info
    console.log('⚠️ RxNorm failed, using fallback')
    const fallbackResult = getFallbackMedicineInfo(medicineName)
    
    // Log the lookup attempt
    if (userId) {
      const supabase = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
      )
      
      EdgeRuntime.waitUntil(
        supabase.from('system_logs').insert({
          log_type: 'medicine_lookup',
          severity: rxnormResult.success ? 'info' : 'warning',
          message: `Medicine lookup: ${medicineName}`,
          error_details: rxnormResult.success ? null : { error: rxnormResult.error },
          user_id: userId
        })
      )
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: fallbackResult,
        source: 'fallback'
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('❌ Medicine lookup error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Failed to lookup medicine information',
        message: 'Medicine lookup service is temporarily unavailable. Please try again in a few moments.',
        suggestions: [
          'Check your internet connection',
          'Verify the medicine name spelling',
          'Try using the generic name instead of brand name',
          'Contact support if the issue persists'
        ]
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

async function lookupRxNorm(medicineName: string) {
  try {
    const cleanName = medicineName.replace(/[^\w\s]/g, '').trim()
    const url = `https://rxnav.nlm.nih.gov/REST/drugs.json?name=${encodeURIComponent(cleanName)}`
    
    console.log(`🌐 Querying RxNorm: ${url}`)
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'MediVision-Assist/1.0'
      }
    })

    if (!response.ok) {
      throw new Error(`RxNorm API error: ${response.status}`)
    }

    const data: RxNormResponse = await response.json()
    
    const concepts = data.drugGroup?.conceptGroup?.[0]?.conceptProperties
    if (!concepts || concepts.length === 0) {
      return { success: false, error: 'No medicine found in RxNorm' }
    }

    const medicine = concepts[0]
    
    // Get additional details
    const detailsUrl = `https://rxnav.nlm.nih.gov/REST/rxcui/${medicine.rxcui}/properties.json`
    let additionalInfo = {}
    
    try {
      const detailsResponse = await fetch(detailsUrl)
      if (detailsResponse.ok) {
        const detailsData = await detailsResponse.json()
        additionalInfo = detailsData.properties || {}
      }
    } catch (e) {
      console.log('Could not fetch additional details:', e)
    }

    return {
      success: true,
      data: {
        medicine: {
          product: medicine.name,
          type: medicine.tty || 'Medicine',
          usage: getUsageInfo(medicine.name),
          dosage: getDosageInfo(medicine.name),
          warnings: getWarnings(medicine.name),
          similar: medicine.synonym || 'None available'
        },
        rxcui: medicine.rxcui,
        confidence: 95
      }
    }

  } catch (error) {
    console.error('RxNorm lookup failed:', error)
    return { 
      success: false, 
      error: error.message,
      message: 'Unable to connect to medicine database. Please check your internet connection and try again.',
      suggestions: ['Check spelling of medicine name', 'Try using generic name instead of brand name', 'Try again in a few moments']
    }
  }
}

function getFallbackMedicineInfo(medicineName: string) {
  const cleanName = medicineName.toLowerCase()
  
  // Basic medicine categories
  const medicineTypes = {
    'aspirin': { type: 'Pain reliever', usage: 'Pain relief and fever reduction' },
    'ibuprofen': { type: 'NSAID', usage: 'Pain relief and anti-inflammatory' },
    'paracetamol': { type: 'Pain reliever', usage: 'Pain relief and fever reduction' },
    'acetaminophen': { type: 'Pain reliever', usage: 'Pain relief and fever reduction' },
    'vitamin': { type: 'Supplement', usage: 'Nutritional supplement' },
    'calcium': { type: 'Supplement', usage: 'Bone health supplement' },
    'magnesium': { type: 'Supplement', usage: 'Mineral supplement' }
  }

  let medicineInfo = {
    type: 'Medicine',
    usage: 'Please consult healthcare provider for usage information'
  }

  for (const [key, info] of Object.entries(medicineTypes)) {
    if (cleanName.includes(key)) {
      medicineInfo = info
      break
    }
  }

  return {
    medicine: {
      product: medicineName,
      type: medicineInfo.type,
      usage: medicineInfo.usage,
      dosage: 'Consult healthcare provider for proper dosage',
      warnings: 'Always read the label and consult a healthcare provider',
      similar: 'Consult pharmacist for alternatives'
    },
    confidence: 60
  }
}

function getUsageInfo(medicineName: string): string {
  const name = medicineName.toLowerCase()
  if (name.includes('aspirin')) return 'Pain relief, fever reduction, and anti-inflammatory'
  if (name.includes('ibuprofen')) return 'Pain relief, fever reduction, and anti-inflammatory'
  if (name.includes('paracetamol') || name.includes('acetaminophen')) return 'Pain relief and fever reduction'
  return 'Consult healthcare provider for usage information'
}

function getDosageInfo(medicineName: string): string {
  return 'Follow package instructions or consult healthcare provider'
}

function getWarnings(medicineName: string): string {
  const name = medicineName.toLowerCase()
  if (name.includes('aspirin')) return 'Not recommended for children under 16. Consult doctor if pregnant.'
  if (name.includes('ibuprofen')) return 'Take with food. Not recommended in third trimester of pregnancy.'
  return 'Read all warnings on package. Consult healthcare provider if unsure.'
}
