// Test script for backend fixes
// Run this in Node.js or browser console to test the fixes

const SUPABASE_URL = "https://ygkxdctaraeragizxfbt.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc";

async function testMedicalChatFunction() {
  console.log('🧪 Testing Medical Chat Function...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({
        question: "hello",
        medicineName: "General Medical Assistant",
        userId: "fc37eb65-c52a-4e9e-a718-f2d2b4d8d324"
      })
    });

    console.log(`📡 Response status: ${response.status}`);
    
    const data = await response.json();
    console.log('📥 Response data:', data);
    
    if (response.ok && data.success) {
      console.log('✅ Medical Chat Function: PASSED');
      console.log(`💬 Reply: ${data.reply.substring(0, 100)}...`);
    } else {
      console.log('❌ Medical Chat Function: FAILED');
      console.log('Error:', data.error || 'Unknown error');
    }
    
  } catch (error) {
    console.log('❌ Medical Chat Function: FAILED');
    console.error('Network error:', error.message);
  }
}

async function testProfilesQuery() {
  console.log('🧪 Testing Profiles Query...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/profiles?select=is_admin&id=eq.fc37eb65-c52a-4e9e-a718-f2d2b4d8d324`, {
      method: 'GET',
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`📡 Response status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('📥 Response data:', data);
      console.log('✅ Profiles Query: PASSED');
    } else {
      const errorText = await response.text();
      console.log('❌ Profiles Query: FAILED');
      console.log('Error:', errorText);
    }
    
  } catch (error) {
    console.log('❌ Profiles Query: FAILED');
    console.error('Network error:', error.message);
  }
}

async function testRLSPolicies() {
  console.log('🧪 Testing RLS Policies (requires authentication)...');
  
  // This test requires a valid user session
  // You would need to authenticate first to test this properly
  console.log('ℹ️ This test requires authentication. Run from the app after login.');
}

async function runAllTests() {
  console.log('🚀 Starting Backend Tests...\n');
  
  await testMedicalChatFunction();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await testProfilesQuery();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await testRLSPolicies();
  
  console.log('\n✅ All tests completed!');
}

// Export for use in browser or Node.js
if (typeof window !== 'undefined') {
  // Browser environment - make functions available globally
  window.testMedicalChatFunction = testMedicalChatFunction;
  window.testProfilesQuery = testProfilesQuery;
  window.testRLSPolicies = testRLSPolicies;
  window.runAllTests = runAllTests;

  console.log('🔧 Test functions loaded. Run runAllTests() to start testing.');
} else {
  // Node.js environment - auto-run tests
  runAllTests();
}
