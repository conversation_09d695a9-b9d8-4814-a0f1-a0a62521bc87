/**
 * Test script to verify the chatbot identity fix
 * Tests various ways users might ask about the creator/developer
 */

const SUPABASE_URL = 'https://ygkxdctaraeragizxfbt.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'

async function testIdentityQuestion(question, testName) {
    console.log(`\n🧪 Testing: ${testName}`)
    console.log(`📝 Question: "${question}"`)
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
                question: question,
                userId: 'test-identity-user'
            })
        })

        const data = await response.json()
        
        if (data.success) {
            console.log(`✅ Response: ${data.reply}`)
            
            // Check if the response contains the correct identity
            const correctIdentity = data.reply.includes('Med Amine Chouchane')
            const incorrectIdentity = data.reply.includes('Medine Chane') || data.reply.includes('Google DeepMind') || data.reply.includes('Gemma')
            
            if (correctIdentity && !incorrectIdentity) {
                console.log(`🎯 IDENTITY CHECK: ✅ CORRECT`)
            } else if (incorrectIdentity) {
                console.log(`🎯 IDENTITY CHECK: ❌ INCORRECT - Contains wrong identity`)
            } else if (!correctIdentity) {
                console.log(`🎯 IDENTITY CHECK: ⚠️ MISSING - No identity mentioned`)
            }
            
            console.log(`📍 Source: ${data.source || 'openrouter'}`)
        } else {
            console.log(`❌ Error: ${data.error}`)
            if (data.reply) {
                console.log(`💬 Fallback reply: ${data.reply}`)
            }
        }
        
        return data
    } catch (error) {
        console.log(`❌ Network error: ${error.message}`)
        return null
    }
}

async function runIdentityTests() {
    console.log('🚀 Starting Identity Fix Validation Tests')
    console.log('=' .repeat(60))
    
    // Test various ways users might ask about the creator
    const identityQuestions = [
        { question: "Who created you?", name: "Direct Creator Question" },
        { question: "Who made you?", name: "Who Made You" },
        { question: "Who developed you?", name: "Who Developed You" },
        { question: "Who built you?", name: "Who Built You" },
        { question: "Who is your creator?", name: "Your Creator" },
        { question: "Who is your developer?", name: "Your Developer" },
        { question: "Who are you?", name: "Who Are You" },
        { question: "What is your name?", name: "Your Name" },
        { question: "Tell me about yourself", name: "About Yourself" },
        { question: "Who designed you?", name: "Who Designed You" }
    ]
    
    let correctResponses = 0
    let totalTests = identityQuestions.length
    
    for (const test of identityQuestions) {
        const result = await testIdentityQuestion(test.question, test.name)
        
        if (result && result.success && result.reply.includes('Med Amine Chouchane')) {
            correctResponses++
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    console.log('\n' + '=' .repeat(60))
    console.log('🏁 Identity Fix Test Results')
    console.log(`✅ Correct responses: ${correctResponses}/${totalTests}`)
    console.log(`📊 Success rate: ${Math.round((correctResponses/totalTests) * 100)}%`)
    
    if (correctResponses === totalTests) {
        console.log('🎉 ALL TESTS PASSED! Identity fix is working correctly.')
    } else if (correctResponses > totalTests * 0.8) {
        console.log('⚠️ Most tests passed, but some issues remain.')
    } else {
        console.log('❌ Identity fix needs more work.')
    }
    
    console.log('\n📋 Next steps:')
    console.log('1. If tests failed, check the system prompt in medical-chat/index.ts')
    console.log('2. Verify the fallback knowledge base has correct identity responses')
    console.log('3. Test in the actual application UI')
}

// Run the tests
runIdentityTests().catch(console.error)
