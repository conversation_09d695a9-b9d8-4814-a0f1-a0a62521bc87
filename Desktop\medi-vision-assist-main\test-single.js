/**
 * Single test to check specific behavior
 */

const SUPABASE_URL = 'https://ygkxdctaraeragizxfbt.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'

async function testSingle() {
    console.log('🧪 Testing medical question with API/fallback...')
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
                question: 'hi',
                userId: 'test-user'
            })
        })

        console.log('📡 Response status:', response.status)
        const data = await response.json()
        console.log('📄 Full response data:', JSON.stringify(data, null, 2))

        if (data.success) {
            console.log('✅ Response:', data.reply)
            if (data.source) {
                console.log('📍 Source:', data.source)
            }
            if (data.note) {
                console.log('📝 Note:', data.note)
            }
        } else {
            console.log('❌ Error:', data.error)
            console.log('💬 Fallback reply:', data.reply)
        }
        
    } catch (error) {
        console.log('❌ Network error:', error.message)
    }
}

testSingle()
