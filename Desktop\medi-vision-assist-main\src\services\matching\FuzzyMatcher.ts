/**
 * Fuzzy string matching service for medicine name recognition
 * Uses Levenshtein distance and other algorithms for accurate matching
 */

export interface FuzzyMatchResult {
  match: string;
  confidence: number;
  distance: number;
  algorithm: string;
}

export interface FuzzyMatchOptions {
  threshold?: number; // Minimum confidence threshold (0-1)
  maxDistance?: number; // Maximum Levenshtein distance
  caseSensitive?: boolean;
  includePartialMatches?: boolean;
}

export class FuzzyMatcher {
  private static commonMedicines = [
    // Pain relievers
    'Aspirin', 'Ibuprofen', 'Paracetamol', 'Acetaminophen', 'Naproxen', 'Diclofenac',
    'Tramadol', 'Codeine', 'Morphine', 'Fentanyl',
    
    // Antibiotics
    'Amoxicillin', 'Penicillin', 'Azithromycin', 'Ciprofloxacin', 'Doxycycline',
    'Cephalexin', 'Clindamycin', 'Metronidazole', 'Vancomycin',
    
    // Cardiovascular
    'Lisinopril', 'Metoprolol', 'Amlodipine', 'Atorvastatin', 'Simvastatin',
    'Warfarin', 'Clopidogrel', 'Digoxin', 'Furosemide',
    
    // Diabetes
    'Metformin', 'Insulin', 'Glipizide', 'Glyburide', 'Pioglitazone',
    
    // Respiratory
    'Albuterol', 'Prednisone', 'Montelukast', 'Fluticasone', 'Budesonide',
    
    // Gastrointestinal
    'Omeprazole', 'Pantoprazole', 'Ranitidine', 'Lansoprazole', 'Esomeprazole',
    
    // Mental Health
    'Sertraline', 'Fluoxetine', 'Escitalopram', 'Alprazolam', 'Lorazepam',
    'Clonazepam', 'Zolpidem', 'Trazodone',
    
    // Common brands
    'Tylenol', 'Advil', 'Motrin', 'Aleve', 'Bayer', 'Excedrin',
    'Prilosec', 'Nexium', 'Zantac', 'Pepcid', 'Tums', 'Rolaids',
    'Claritin', 'Zyrtec', 'Allegra', 'Benadryl', 'Sudafed',
    'Mucinex', 'Robitussin', 'Delsym', 'Vicks',
    
    // French medicines (common in French-speaking regions)
    'Doliprane', 'Efferalgan', 'Dafalgan', 'Fervex', 'Actifed',
    'Rhinadvil', 'Humex', 'Toplexil', 'Biafine', 'Voltaren',
    
    // German medicines
    'Aspirin', 'Ibuprofen', 'Paracetamol', 'Voltaren', 'Thomapyrin',
    'Buscopan', 'Iberogast', 'Sinupret', 'Bronchicum'
  ];

  /**
   * Find the best fuzzy match for a given text against known medicines
   */
  static findBestMatch(
    input: string, 
    candidates: string[] = FuzzyMatcher.commonMedicines,
    options: FuzzyMatchOptions = {}
  ): FuzzyMatchResult | null {
    const opts = {
      threshold: 0.6,
      maxDistance: 3,
      caseSensitive: false,
      includePartialMatches: true,
      ...options
    };

    if (!input || input.trim().length === 0) return null;

    const cleanInput = opts.caseSensitive ? input.trim() : input.trim().toLowerCase();
    let bestMatch: FuzzyMatchResult | null = null;

    for (const candidate of candidates) {
      const cleanCandidate = opts.caseSensitive ? candidate : candidate.toLowerCase();
      
      // Try multiple matching algorithms
      const results = [
        this.levenshteinMatch(cleanInput, cleanCandidate, candidate),
        this.jaroWinklerMatch(cleanInput, cleanCandidate, candidate),
        this.substringMatch(cleanInput, cleanCandidate, candidate),
        this.soundexMatch(cleanInput, cleanCandidate, candidate)
      ];

      // Find the best result from all algorithms
      const bestResult = results.reduce((best, current) => 
        current.confidence > best.confidence ? current : best
      );

      // Update overall best match if this is better
      if (bestResult.confidence >= opts.threshold && 
          (!bestMatch || bestResult.confidence > bestMatch.confidence)) {
        bestMatch = bestResult;
      }
    }

    return bestMatch;
  }

  /**
   * Find multiple potential matches with confidence scores
   */
  static findMultipleMatches(
    input: string,
    candidates: string[] = FuzzyMatcher.commonMedicines,
    options: FuzzyMatchOptions = {},
    maxResults: number = 5
  ): FuzzyMatchResult[] {
    const opts = {
      threshold: 0.4, // Lower threshold for multiple matches
      ...options
    };

    if (!input || input.trim().length === 0) return [];

    const cleanInput = opts.caseSensitive ? input.trim() : input.trim().toLowerCase();
    const matches: FuzzyMatchResult[] = [];

    for (const candidate of candidates) {
      const cleanCandidate = opts.caseSensitive ? candidate : candidate.toLowerCase();
      
      const results = [
        this.levenshteinMatch(cleanInput, cleanCandidate, candidate),
        this.jaroWinklerMatch(cleanInput, cleanCandidate, candidate),
        this.substringMatch(cleanInput, cleanCandidate, candidate)
      ];

      const bestResult = results.reduce((best, current) => 
        current.confidence > best.confidence ? current : best
      );

      if (bestResult.confidence >= opts.threshold) {
        matches.push(bestResult);
      }
    }

    // Sort by confidence and return top results
    return matches
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, maxResults);
  }

  /**
   * Levenshtein distance-based matching
   */
  private static levenshteinMatch(input: string, candidate: string, original: string): FuzzyMatchResult {
    const distance = this.levenshteinDistance(input, candidate);
    const maxLength = Math.max(input.length, candidate.length);
    const confidence = maxLength === 0 ? 1 : 1 - (distance / maxLength);

    return {
      match: original,
      confidence: Math.max(0, confidence),
      distance,
      algorithm: 'levenshtein'
    };
  }

  /**
   * Jaro-Winkler similarity-based matching
   */
  private static jaroWinklerMatch(input: string, candidate: string, original: string): FuzzyMatchResult {
    const similarity = this.jaroWinklerSimilarity(input, candidate);
    
    return {
      match: original,
      confidence: similarity,
      distance: Math.round((1 - similarity) * Math.max(input.length, candidate.length)),
      algorithm: 'jaro-winkler'
    };
  }

  /**
   * Substring-based matching (for partial matches)
   */
  private static substringMatch(input: string, candidate: string, original: string): FuzzyMatchResult {
    let confidence = 0;

    // Check if input is contained in candidate
    if (candidate.includes(input)) {
      confidence = input.length / candidate.length;
    }
    // Check if candidate is contained in input
    else if (input.includes(candidate)) {
      confidence = candidate.length / input.length;
    }
    // Check for common substrings
    else {
      const commonLength = this.longestCommonSubstring(input, candidate).length;
      confidence = commonLength / Math.max(input.length, candidate.length);
    }

    return {
      match: original,
      confidence: confidence * 0.8, // Slightly lower weight for substring matches
      distance: Math.abs(input.length - candidate.length),
      algorithm: 'substring'
    };
  }

  /**
   * Soundex-based phonetic matching
   */
  private static soundexMatch(input: string, candidate: string, original: string): FuzzyMatchResult {
    const inputSoundex = this.soundex(input);
    const candidateSoundex = this.soundex(candidate);
    
    const confidence = inputSoundex === candidateSoundex ? 0.7 : 0;

    return {
      match: original,
      confidence,
      distance: confidence === 0 ? 999 : 0,
      algorithm: 'soundex'
    };
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Calculate Jaro-Winkler similarity
   */
  private static jaroWinklerSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;

    const matchWindow = Math.floor(Math.max(str1.length, str2.length) / 2) - 1;
    const str1Matches = new Array(str1.length).fill(false);
    const str2Matches = new Array(str2.length).fill(false);

    let matches = 0;
    let transpositions = 0;

    // Find matches
    for (let i = 0; i < str1.length; i++) {
      const start = Math.max(0, i - matchWindow);
      const end = Math.min(i + matchWindow + 1, str2.length);

      for (let j = start; j < end; j++) {
        if (str2Matches[j] || str1[i] !== str2[j]) continue;
        str1Matches[i] = true;
        str2Matches[j] = true;
        matches++;
        break;
      }
    }

    if (matches === 0) return 0;

    // Find transpositions
    let k = 0;
    for (let i = 0; i < str1.length; i++) {
      if (!str1Matches[i]) continue;
      while (!str2Matches[k]) k++;
      if (str1[i] !== str2[k]) transpositions++;
      k++;
    }

    const jaro = (matches / str1.length + matches / str2.length + (matches - transpositions / 2) / matches) / 3;

    // Jaro-Winkler prefix bonus
    let prefix = 0;
    for (let i = 0; i < Math.min(str1.length, str2.length, 4); i++) {
      if (str1[i] === str2[i]) prefix++;
      else break;
    }

    return jaro + (0.1 * prefix * (1 - jaro));
  }

  /**
   * Find longest common substring
   */
  private static longestCommonSubstring(str1: string, str2: string): string {
    let longest = '';
    for (let i = 0; i < str1.length; i++) {
      for (let j = i + 1; j <= str1.length; j++) {
        const substring = str1.slice(i, j);
        if (str2.includes(substring) && substring.length > longest.length) {
          longest = substring;
        }
      }
    }
    return longest;
  }

  /**
   * Generate Soundex code for phonetic matching
   */
  private static soundex(str: string): string {
    if (!str) return '';
    
    const code = str.toUpperCase().replace(/[^A-Z]/g, '');
    if (code.length === 0) return '';

    let soundex = code[0];
    const mapping: { [key: string]: string } = {
      'BFPV': '1', 'CGJKQSXZ': '2', 'DT': '3',
      'L': '4', 'MN': '5', 'R': '6'
    };

    for (let i = 1; i < code.length; i++) {
      for (const [chars, digit] of Object.entries(mapping)) {
        if (chars.includes(code[i])) {
          if (soundex[soundex.length - 1] !== digit) {
            soundex += digit;
          }
          break;
        }
      }
    }

    return (soundex + '000').slice(0, 4);
  }
}
