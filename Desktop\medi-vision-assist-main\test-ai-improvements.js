/**
 * Test script to verify AI improvements for MediVision chatbot
 * Tests error handling, relevance checking, and language guidance
 */

const SUPABASE_URL = 'https://ygkxdctaraeragizxfbt.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'

async function testChatbot(question, testName) {
    console.log(`\n🧪 Testing: ${testName}`)
    console.log(`📝 Question: "${question}"`)
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
                question: question,
                userId: 'test-user-123'
            })
        })

        const data = await response.json()
        
        if (data.success) {
            console.log(`✅ Response: ${data.reply}`)
        } else {
            console.log(`❌ Error: ${data.error}`)
            console.log(`💬 Fallback reply: ${data.reply}`)
        }
        
        return data
    } catch (error) {
        console.log(`❌ Network error: ${error.message}`)
        return null
    }
}

async function runTests() {
    console.log('🚀 Starting MediVision AI Improvements Test Suite')
    console.log('=' .repeat(60))

    // Test 1: Error Handling - Empty/unclear input
    await testChatbot('', 'Empty Input Handling')
    await testChatbot('...', 'Unclear Input (dots)')
    await testChatbot(':', 'Unclear Input (colon)')
    await testChatbot('asdfgh', 'Nonsensical Input')

    // Test 2: Language Policy
    await testChatbot('مرحبا، كيف حالك؟', 'Arabic Language Detection')
    await testChatbot('Hallo, wie geht es dir?', 'German Language Detection')
    await testChatbot('Bonjour, comment allez-vous?', 'French Language Detection')

    // Test 3: Topic Relevance - Non-medical questions
    await testChatbot('Tell me a joke', 'Non-medical: Joke Request')
    await testChatbot('How do I cook chicken breast?', 'Non-medical: Cooking Question')
    await testChatbot('What is the capital of France?', 'Non-medical: Geography Question')
    await testChatbot('Can you help me with math homework?', 'Non-medical: Education Question')

    // Test 4: Friendly Prompting for vague queries
    await testChatbot('can you talk?', 'Vague Query: Can you talk')
    await testChatbot('hello', 'Vague Query: Hello')
    await testChatbot('hi', 'Vague Query: Hi')
    await testChatbot('are you there?', 'Vague Query: Are you there')

    // Test 5: Valid medical questions (should work normally)
    await testChatbot('What is aspirin used for?', 'Valid Medical: Aspirin Question')
    await testChatbot('What are the side effects of ibuprofen?', 'Valid Medical: Ibuprofen Side Effects')
    await testChatbot('How can I prevent heart disease?', 'Valid Medical: Prevention Question')

    // Test 6: Identity verification
    await testChatbot('Who are you?', 'Identity: Who are you')
    await testChatbot('Who created you?', 'Identity: Who created you')
    await testChatbot('What is your name?', 'Identity: What is your name')

    console.log('\n' + '=' .repeat(60))
    console.log('🏁 Test suite completed!')
    console.log('📋 Review the responses above to verify improvements are working correctly.')
}

// Run the tests
runTests().catch(console.error)
