/**
 * Debug script to investigate why identity responses are not working
 * This will help us understand what's happening in the deployed function
 */

const SUPABASE_URL = 'https://ygkxdctaraeragizxfbt.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc'

async function debugIdentityResponse(question) {
    console.log(`\n🔍 Debugging: "${question}"`)
    console.log('=' .repeat(50))
    
    try {
        const startTime = Date.now()
        
        const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
                question: question,
                medicineName: 'General Medical Assistant',
                userId: 'debug-user-123'
            })
        })

        const responseTime = Date.now() - startTime
        console.log(`📡 Response Status: ${response.status}`)
        console.log(`⏱️ Response Time: ${responseTime}ms`)
        
        if (!response.ok) {
            const errorText = await response.text()
            console.log(`❌ HTTP Error: ${errorText}`)
            return null
        }

        const data = await response.json()
        console.log(`📊 Response Data:`, JSON.stringify(data, null, 2))
        
        if (data.success) {
            console.log(`✅ Success: true`)
            console.log(`💬 Reply: "${data.reply}"`)
            console.log(`📍 Source: ${data.source || 'openrouter'}`)
            console.log(`⏱️ Function Response Time: ${data.responseTime || 'N/A'}ms`)
            
            // Analyze the response
            const reply = data.reply.toLowerCase()
            
            if (reply.includes('med amine chouchane')) {
                console.log(`🎯 IDENTITY: ✅ CORRECT - Contains "Med Amine Chouchane"`)
            } else if (reply.includes('medine chane')) {
                console.log(`🎯 IDENTITY: ❌ INCORRECT - Contains "Medine Chane"`)
            } else if (reply.includes('google') || reply.includes('deepmind') || reply.includes('gemma')) {
                console.log(`🎯 IDENTITY: ❌ INCORRECT - Contains Google/DeepMind/Gemma`)
            } else if (reply.includes('medivision')) {
                console.log(`🎯 IDENTITY: ⚠️ PARTIAL - Only mentions MediVision, no creator`)
            } else {
                console.log(`🎯 IDENTITY: ❌ MISSING - No identity information`)
            }
            
            // Check if it's a generic response
            if (reply.includes('medical assistant') && reply.includes('help') && !reply.includes('developed') && !reply.includes('created')) {
                console.log(`🤖 RESPONSE TYPE: Generic medical assistant response (not identity-specific)`)
            } else if (reply.includes('developed') || reply.includes('created') || reply.includes('made')) {
                console.log(`🤖 RESPONSE TYPE: Identity-specific response`)
            } else {
                console.log(`🤖 RESPONSE TYPE: Other`)
            }
            
        } else {
            console.log(`❌ Success: false`)
            console.log(`💥 Error: ${data.error}`)
            if (data.reply) {
                console.log(`💬 Fallback Reply: "${data.reply}"`)
            }
        }
        
        return data
        
    } catch (error) {
        console.log(`❌ Network Error: ${error.message}`)
        return null
    }
}

async function runDebugAnalysis() {
    console.log('🚀 Starting Identity Bug Debug Analysis')
    console.log('🎯 Goal: Understand why identity responses are not working')
    console.log('=' .repeat(60))
    
    // Test various identity questions
    const identityQuestions = [
        'who create you',
        'who made you', 
        'who developed you',
        'who created you?',
        'who is your creator',
        'who built you',
        'tell me who made you',
        'what is your creator name',
        'who designed you'
    ]
    
    console.log(`📋 Testing ${identityQuestions.length} identity questions...`)
    
    let correctResponses = 0
    let genericResponses = 0
    let incorrectResponses = 0
    
    for (const question of identityQuestions) {
        const result = await debugIdentityResponse(question)
        
        if (result && result.success) {
            const reply = result.reply.toLowerCase()
            
            if (reply.includes('med amine chouchane')) {
                correctResponses++
            } else if (reply.includes('medical assistant') && reply.includes('help') && !reply.includes('developed')) {
                genericResponses++
            } else {
                incorrectResponses++
            }
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1500))
    }
    
    console.log('\n' + '=' .repeat(60))
    console.log('📊 DEBUG ANALYSIS RESULTS')
    console.log('=' .repeat(60))
    console.log(`✅ Correct Identity Responses: ${correctResponses}/${identityQuestions.length}`)
    console.log(`⚠️ Generic Responses: ${genericResponses}/${identityQuestions.length}`)
    console.log(`❌ Incorrect Responses: ${incorrectResponses}/${identityQuestions.length}`)
    
    console.log('\n🔍 DIAGNOSIS:')
    
    if (correctResponses === identityQuestions.length) {
        console.log('✅ Identity system is working perfectly!')
    } else if (correctResponses > 0) {
        console.log('⚠️ Identity system is partially working - inconsistent responses')
        console.log('🔧 Recommendation: Strengthen system prompt and add post-processing')
    } else if (genericResponses > incorrectResponses) {
        console.log('❌ System is giving generic responses instead of identity-specific ones')
        console.log('🔧 Recommendation: The system prompt is not being followed for identity questions')
        console.log('💡 Likely cause: Function deployment issue or system prompt not updated')
    } else {
        console.log('❌ System is giving incorrect identity information')
        console.log('🔧 Recommendation: Immediate system prompt update and post-processing required')
    }
    
    console.log('\n📋 NEXT STEPS:')
    console.log('1. Check if the Supabase function has the latest system prompt')
    console.log('2. Implement aggressive identity detection and correction')
    console.log('3. Create a comprehensive identity dataset')
    console.log('4. Add multiple layers of identity enforcement')
}

// Run the debug analysis
runDebugAnalysis().catch(console.error)
