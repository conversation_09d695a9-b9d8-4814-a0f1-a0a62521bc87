
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Users, 
  Scan, 
  MessageCircle, 
  Clock, 
  ThumbsUp, 
  ThumbsDown,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import Navigation from '@/components/Navigation';

interface KPIData {
  totalScans: number;
  totalChats: number;
  avgResponseTime: number;
  satisfactionScore: number;
}

interface FeedbackItem {
  id: string;
  medicine_name: string;
  question: string;
  response: string;
  feedback: boolean;
  reviewed: boolean;
  created_at: string;
}

interface SystemLog {
  id: string;
  log_type: string;
  severity: string;
  message: string;
  created_at: string;
  error_details?: any;
}

const AdminDashboard = () => {
  const { user, signOut } = useAuth();
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackItem | null>(null);

  // Fetch KPI data
  const { data: kpiData, isLoading: kpiLoading } = useQuery({
    queryKey: ['admin-kpis'],
    queryFn: async (): Promise<KPIData> => {
      const [scansResult, chatsResult] = await Promise.all([
        supabase.from('medicine_scans').select('id', { count: 'exact' }),
        supabase.from('chat_sessions').select('response_time_ms', { count: 'exact' })
      ]);

      const { data: feedbackData } = await supabase
        .from('chat_feedback')
        .select('feedback')
        .not('feedback', 'is', null);

      const totalScans = scansResult.count || 0;
      const totalChats = chatsResult.count || 0;
      const avgResponseTime = chatsResult.data?.length 
        ? Math.round(chatsResult.data.reduce((sum, chat) => sum + (chat.response_time_ms || 0), 0) / chatsResult.data.length)
        : 0;
      
      const positivesFeedback = feedbackData?.filter(f => f.feedback === true).length || 0;
      const totalFeedback = feedbackData?.length || 0;
      const satisfactionScore = totalFeedback > 0 ? Math.round((positivesFeedback / totalFeedback) * 100) : 0;

      return {
        totalScans,
        totalChats,
        avgResponseTime,
        satisfactionScore
      };
    }
  });

  // Fetch feedback data
  const { data: feedbackData, isLoading: feedbackLoading, refetch: refetchFeedback } = useQuery({
    queryKey: ['admin-feedback'],
    queryFn: async (): Promise<FeedbackItem[]> => {
      const { data, error } = await supabase
        .from('chat_feedback')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      return data || [];
    }
  });

  // Fetch system logs
  const { data: systemLogs, isLoading: logsLoading } = useQuery({
    queryKey: ['admin-logs'],
    queryFn: async (): Promise<SystemLog[]> => {
      const { data, error } = await supabase
        .from('system_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      return data || [];
    }
  });

  const markFeedbackAsReviewed = async (feedbackId: string) => {
    const { error } = await supabase
      .from('chat_feedback')
      .update({ reviewed: true })
      .eq('id', feedbackId);

    if (!error) {
      refetchFeedback();
    }
  };

  // Chart data for medicine scans over time
  const chartData = [
    { name: 'Mon', scans: 12, chats: 8 },
    { name: 'Tue', scans: 19, chats: 15 },
    { name: 'Wed', scans: 15, chats: 12 },
    { name: 'Thu', scans: 22, chats: 18 },
    { name: 'Fri', scans: 28, chats: 22 },
    { name: 'Sat', scans: 16, chats: 14 },
    { name: 'Sun', scans: 11, chats: 9 }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="text-muted-foreground mt-2">
              Welcome back, {user?.email}
            </p>
          </div>
          <Button onClick={signOut} variant="outline">
            Sign Out
          </Button>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
              <Scan className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {kpiLoading ? '...' : kpiData?.totalScans.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Medicine identifications
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Chat Sessions</CardTitle>
              <MessageCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {kpiLoading ? '...' : kpiData?.totalChats.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                AI conversations
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {kpiLoading ? '...' : `${kpiData?.avgResponseTime}ms`}
              </div>
              <p className="text-xs text-muted-foreground">
                GPT response time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Satisfaction</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {kpiLoading ? '...' : `${kpiData?.satisfactionScore}%`}
              </div>
              <p className="text-xs text-muted-foreground">
                Positive feedback
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Activity</CardTitle>
              <CardDescription>Scans and chats over the past week</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="scans" fill="#3b82f6" name="Scans" />
                  <Bar dataKey="chats" fill="#10b981" name="Chats" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Response Time Trend</CardTitle>
              <CardDescription>AI response performance</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="chats" stroke="#f59e0b" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Tabs for Feedback and Logs */}
        <Tabs defaultValue="feedback" className="space-y-4">
          <TabsList>
            <TabsTrigger value="feedback">User Feedback</TabsTrigger>
            <TabsTrigger value="logs">System Logs</TabsTrigger>
          </TabsList>

          <TabsContent value="feedback">
            <Card>
              <CardHeader>
                <CardTitle>User Feedback</CardTitle>
                <CardDescription>
                  Review user feedback on AI responses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <div className="space-y-4">
                    {feedbackLoading ? (
                      <p>Loading feedback...</p>
                    ) : feedbackData?.length === 0 ? (
                      <p className="text-muted-foreground">No feedback available yet.</p>
                    ) : (
                      feedbackData?.map((feedback) => (
                        <div key={feedback.id} className="border rounded-lg p-4 space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Badge variant={feedback.feedback ? "default" : "destructive"}>
                                {feedback.feedback ? (
                                  <><ThumbsUp className="h-3 w-3 mr-1" /> Positive</>
                                ) : (
                                  <><ThumbsDown className="h-3 w-3 mr-1" /> Negative</>
                                )}
                              </Badge>
                              <Badge variant={feedback.reviewed ? "secondary" : "outline"}>
                                {feedback.reviewed ? (
                                  <><CheckCircle className="h-3 w-3 mr-1" /> Reviewed</>
                                ) : (
                                  'Pending Review'
                                )}
                              </Badge>
                            </div>
                            <span className="text-sm text-muted-foreground">
                              {new Date(feedback.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          
                          <div>
                            <p className="font-medium">Medicine: {feedback.medicine_name}</p>
                            <p className="text-sm text-muted-foreground mt-1">
                              Q: {feedback.question}
                            </p>
                            <p className="text-sm mt-2 p-2 bg-muted rounded">
                              A: {feedback.response}
                            </p>
                          </div>

                          {!feedback.reviewed && (
                            <Button
                              size="sm"
                              onClick={() => markFeedbackAsReviewed(feedback.id)}
                            >
                              Mark as Reviewed
                            </Button>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logs">
            <Card>
              <CardHeader>
                <CardTitle>System Logs</CardTitle>
                <CardDescription>
                  Monitor system activity and errors
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <div className="space-y-2">
                    {logsLoading ? (
                      <p>Loading system logs...</p>
                    ) : systemLogs?.length === 0 ? (
                      <p className="text-muted-foreground">No system logs available.</p>
                    ) : (
                      systemLogs?.map((log) => (
                        <div key={log.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                          <div className="flex-shrink-0">
                            {log.severity === 'error' ? (
                              <AlertTriangle className="h-4 w-4 text-destructive" />
                            ) : log.severity === 'warning' ? (
                              <AlertTriangle className="h-4 w-4 text-yellow-500" />
                            ) : (
                              <Activity className="h-4 w-4 text-muted-foreground" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium">{log.log_type}</p>
                              <span className="text-xs text-muted-foreground">
                                {new Date(log.created_at).toLocaleString()}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">{log.message}</p>
                            {log.error_details && (
                              <details className="mt-2">
                                <summary className="text-xs cursor-pointer">Error Details</summary>
                                <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                                  {JSON.stringify(log.error_details, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
