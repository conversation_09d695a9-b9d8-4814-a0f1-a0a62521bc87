# Debugging Guide for MediVision Assist

## Common Issues and Solutions

### 1. Medical Chat Function 500 Errors

#### Symptoms:
- `POST https://ygkxdctaraeragizxfbt.supabase.co/functions/v1/medical-chat 500 (Internal Server Error)`
- <PERSON><PERSON> returns: "I apologize, but I'm having trouble processing your request right now..."

#### Debugging Steps:

1. **Check Environment Variables:**
   ```bash
   # In Supabase Dashboard > Settings > Edge Functions > Environment Variables
   # Ensure these are set:
   OPENROUTER_API_KEY=sk-or-v1-...
   SUPABASE_URL=https://ygkxdctaraeragizxfbt.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

2. **Check Function Logs:**
   ```bash
   # If you have Supabase CLI installed:
   supabase functions logs medical-chat
   
   # Or check in Supabase Dashboard > Edge Functions > medical-chat > Logs
   ```

3. **Test the Function Directly:**
   ```javascript
   // In browser console:
   const response = await fetch('https://ygkxdctaraeragizxfbt.supabase.co/functions/v1/medical-chat', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
       'Authorization': 'Bearer YOUR_ANON_KEY'
     },
     body: JSON.stringify({
       question: 'What is aspirin?',
       medicineName: 'General Medical Assistant',
       userId: 'test-user-id'
     })
   });
   console.log(await response.json());
   ```

#### Common Fixes:
- **Missing API Key**: Set `OPENROUTER_API_KEY` in Supabase environment variables
- **Invalid API Key**: Verify the OpenRouter API key is valid and has credits
- **Supabase Config**: Ensure `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY` are set

### 2. Profiles Table 500 Errors

#### Symptoms:
- `GET https://ygkxdctaraeragizxfbt.supabase.co/rest/v1/profiles?select=is_admin&id=eq.USER_ID 500`
- Admin panel not loading
- User profile queries failing

#### Debugging Steps:

1. **Check if Profile Exists:**
   ```sql
   -- In Supabase SQL Editor:
   SELECT * FROM profiles WHERE id = 'USER_ID_HERE';
   ```

2. **Check RLS Policies:**
   ```sql
   -- Verify RLS policies are working:
   SELECT * FROM pg_policies WHERE tablename = 'profiles';
   ```

3. **Test Profile Creation:**
   ```sql
   -- Manually create a profile if missing:
   INSERT INTO profiles (id, email, full_name, is_admin)
   VALUES ('USER_ID_HERE', '<EMAIL>', 'Test User', false);
   ```

#### Common Fixes:
- **Missing Profile**: The trigger might not have created the profile - create manually
- **RLS Policy Issues**: Check if policies allow user to read their own profile
- **Authentication Issues**: Ensure user is properly authenticated

### 3. Environment Setup

#### Required Environment Variables:

**Supabase Edge Functions (.env in supabase/ directory):**
```env
OPENROUTER_API_KEY=sk-or-v1-your-key-here
NODE_ENV=development
```

**Client-side (automatically configured):**
- `SUPABASE_URL`: https://ygkxdctaraeragizxfbt.supabase.co
- `SUPABASE_ANON_KEY`: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### 4. Deployment Checklist

1. **Deploy Edge Functions:**
   ```bash
   supabase functions deploy medical-chat
   supabase functions deploy medicine-lookup
   ```

2. **Set Environment Variables:**
   - Go to Supabase Dashboard > Settings > Edge Functions
   - Add `OPENROUTER_API_KEY`

3. **Verify Database Schema:**
   - Check that all tables exist
   - Verify RLS policies are active
   - Test trigger functions

4. **Test All Endpoints:**
   - Medical chat function
   - Medicine lookup function
   - Profile queries
   - Admin dashboard (if applicable)

### 5. Monitoring and Logs

#### Client-side Debugging:
```javascript
// Enable detailed logging in browser console
localStorage.setItem('debug', 'true');
```

#### Server-side Logs:
- Supabase Dashboard > Edge Functions > Logs
- Look for console.log statements in function code
- Check for error patterns and stack traces

### 6. Common Error Codes

- **500**: Server error - check function logs and environment variables
- **401**: Authentication error - check user session and tokens
- **403**: Permission error - check RLS policies
- **404**: Resource not found - check if endpoints exist

### 7. Support and Further Help

If issues persist:
1. Check the browser console for detailed error messages
2. Review Supabase function logs
3. Verify all environment variables are set correctly
4. Test with a fresh user account
5. Check if the issue is reproducible across different browsers/devices
