/**
 * Simple test to check API response structure
 */

const SUPABASE_URL = 'https://ygkxdctaraeragizxfbt.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

async function testSimple() {
    console.log('🧪 Testing simple medical question...')
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/medical-chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
                question: 'What is aspirin?',
                userId: 'test-user'
            })
        })

        console.log('📡 Response status:', response.status)
        console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()))
        
        const text = await response.text()
        console.log('📄 Raw response text:', text)
        
        try {
            const data = JSON.parse(text)
            console.log('📊 Parsed JSON:', JSON.stringify(data, null, 2))
        } catch (parseError) {
            console.log('❌ Failed to parse JSON:', parseError.message)
        }
        
    } catch (error) {
        console.log('❌ Network error:', error.message)
    }
}

testSimple()
